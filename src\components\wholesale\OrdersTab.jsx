import React, { useState, useEffect, useRef } from 'react';
import { Spin, Button, message, Tag } from 'antd';
import dayjs from 'dayjs';
import { AgGridReact } from 'ag-grid-react';
import { theme<PERSON><PERSON>ham } from 'ag-grid-community';
import OrderDetailsModal from './OrderDetailsModal';
import { collection, getDocs, addDoc, setDoc, doc, onSnapshot, query, orderBy, deleteDoc, where, or } from 'firebase/firestore';
import { db, api } from '../../pages/firebase';
import { useUser } from '../../contexts/UserContext';
import { Row, Col, Tooltip, Select, Popconfirm, Space } from 'antd';
import { DeleteOutlined, SaveOutlined } from '@ant-design/icons';


const OrdersTab = () => {
  const { userData } = useUser();
  const [orderData, setOrderData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [modalItem, setModalItem] = useState({});
  const [savedViews, setSavedViews] = useState([]);
  const [selectedViewId, setSelectedViewId] = useState(null);
  const [applyingView, setApplyingView] = useState(false);
  const gridRef = useRef(null);
  // Save current grid state as a new view (AG Grid full state)
  const handleSaveView = async () => {
    const gridState = gridRef.current.api.getState();
    if (selectedViewId) {
      const view = savedViews.find(v => v.id === selectedViewId);
      if (view) {
        await setDoc(doc(db, 'wholesaleOrdersViews', selectedViewId), { gridState: JSON.parse(JSON.stringify(gridState)), timestamp: Date.now() }, { merge: true });
      }
      message.success('View updated!');
      return;
    }
    const name = prompt('Enter a name for this view:');
    if (!name) return;
    
    if (!userData?.id) {
      message.error('You must be logged in to save views');
      return;
    }
    
    const newDoc = await addDoc(collection(db, 'wholesaleOrdersViews'), { 
      name, 
      gridState: JSON.parse(JSON.stringify(gridState)), 
      timestamp: Date.now(),
      createdBy: userData.id,
      createdByEmail: userData.email,
      isPublic: false
    });
    message.success('View saved!');
    // Note: Views will be automatically updated via the onSnapshot listener
    setSelectedViewId(newDoc.id);
  };

  // Apply a saved view to the grid (set initialState)
  const handleSelectView = id => {
    setSelectedViewId(id);
    const view = savedViews.find(v => v.id === id);
    if (!view || !view.gridState) {
      // message.error('View not found');
      gridRef.current.api.resetColumnState();
      gridRef.current.api.resetColumnGroupState();
      gridRef.current.api.setFilterModel(null);
      gridRef.current.api.setRowGroupColumns([]);
      return;
    };
    setApplyingView(true);
    if (view.gridState?.columnVisibility?.hiddenColIds?.length > 0) {
      gridRef.current.api.setColumnsVisible(
        view.gridState.columnVisibility.hiddenColIds,
        false
      );
    }
    gridRef.current.api.setFilterModel(view.gridState.filter.filterModel);
    if (view.gridState.columnOrder?.orderedColIds?.length > 0) {
      const orderedColIds = view.gridState.columnOrder.orderedColIds;
      gridRef.current.api.moveColumns(orderedColIds, 0);
    }
    gridRef.current.api.setRowGroupColumns(view.gridState.rowGroup?.groupColIds || []);
    setTimeout(() => setApplyingView(false), 500);
  };
  const handleDeleteView = async (id) => {
    await deleteDoc(doc(db, 'wholesaleOrdersViews', id));
    setSavedViews(views => views.filter(v => v.id !== id));
    if (selectedViewId === id) setSelectedViewId(null);
  };

  const changeOrderStatuses = async (ids, status) => {
    // console.log("changing order statuses", ids, status);
    let cnt = 0;
    for (const id of ids) {
      await api.updateNetSuiteRecordOnCall({
        recordType: 'salesOrder',
        id,
        data: { status }
      });
      cnt++;
      message.success(`Changed status of ${cnt} orders to ${status}`);
    }
  };

  // Load saved views from Firestore - only for current user
  useEffect(() => {
    const fetchViews = () => {
      if (!userData?.id) {
        setSavedViews([]);
        return () => {};
      }
      
      // Query to get user's private views + all public views
      const q = query(
        collection(db, 'wholesaleOrdersViews'),
        or(
          where('createdBy', '==', userData.id), // User's own views (private)
          where('isPublic', '==', true) // All public views
        )
      );
      
      const unsub = onSnapshot(q, (snapshot) => {
        setSavedViews(snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() })));
      });
      
      return unsub;
    };
    
    if (userData?.id) {
      const unsubscribe = fetchViews();
      return unsubscribe;
    }
  }, [userData?.id]);

  useEffect(() => {
    const fetchOrderData = async () => {
      try {
        // console.log("fetching order data");
        const query = `
          SELECT
            status AS status,
            document_number AS document_number,
            internal_id AS id,
            customer AS customer,
            po_check_number AS po_num,
            FORMAT_DATE('%Y-%m-%d', date) AS date,
            FORMAT_DATE('%Y-%m-%d', dispatch_date) AS dispatch_date,
            SUM(quantity)*-1 AS total_units,
            SUM(quantitycommitted) AS total_units_committed,
            SUM(quantity_shipped_received) AS total_units_shipped,
            ROUND(SUM(amount)*-1, 2) AS total_dollars
          FROM
            \`hj-reporting.transactions.transactions_netsuite\`
          WHERE
            date >= '${dayjs().subtract(12, 'month').format('YYYY-MM-DD')}'
            AND class = 'Wholesale'
            AND status IN ('Sales Order : Pending Approval', 'Sales Order : Pending Fulfillment', 'Sales Order : Pending Billing/Partially Fulfilled', 'Sales Order : Partially Fulfilled')
            AND Account = '40100 Gross Revenue'
            AND upc IS NOT NULL
          GROUP BY
            status,
            document_number,
            customer,
            internal_id,
            po_check_number,
            date,
            dispatch_date
          ORDER BY
            dispatch_date ASC
        `;
        // console.log("query", query);
        let { data: orderData } = await api.bigQueryRunQueryOnCall({
          options: {
            query,
          }
        });
        // console.log("got query",);
        orderData = orderData
          .map((orderObj) => {
            return {
              status: orderObj['status'],
              id: orderObj['id'],
              document_number: orderObj['document_number'],
              customer: orderObj['customer'],
              po_num: orderObj['po_num'],
              date: orderObj['date'],
              dispatch_date: orderObj['dispatch_date'],
              total_units: orderObj['total_units'],
              total_units_committed: orderObj['total_units_committed'],
              total_units_shipped: orderObj['total_units_shipped'],
              total_dollars: orderObj['total_dollars'],
            };
          })
          .sort((a, b) => {
            return new Date(b.name) - new Date(a.name);
          });
        setOrderData(orderData);
        setLoading(false);
      } catch (err) {
        // console.log('error fetching order data', err);
        setLoading(false);
      }
    };
    fetchOrderData();
  }, []);


  return loading ? (
    <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
      <Spin tip="Loading..." fullscreen />
    </div>
  ) : (
    <div className="card shadow mt-3 mb-4">
        <Col flex="none" className="demandplan-toolbar-right">
          <Tooltip title="Select a saved view">
            <Select
              placeholder="Select Saved View"
              style={{ width: 200 }}
              options={savedViews.map(view => ({ label: view.name, value: view.id }))}
              onChange={handleSelectView}
              allowClear
              value={selectedViewId}
              optionRender={(option) => {
                const savedView = savedViews.find(v => v.id === option.key);
                if (!savedView) return null;
                return <Space style={{ display: 'flex', justifyContent: 'space-between', gap: 4 }}>
                  <span>{option.label}</span>
                  {savedView.createdBy === userData?.id && (
                    <Popconfirm title="Are you sure you want to delete this view?" onConfirm={(e) => {
                      e.stopPropagation();
                      handleDeleteView(savedView.id);
                    }}>
                      <DeleteOutlined style={{ color: 'red' }} />
                    </Popconfirm>
                  )}
                </Space>;
              }}
            />
          </Tooltip>
          <Tooltip title="Save current table configuration (filters, columns, sorting) as a reusable view that you can quickly load later">
              <Button
                icon={<SaveOutlined />}
                type="primary"
                onClick={handleSaveView}
                disabled={applyingView}
              >
                Save View
              </Button>
            </Tooltip>
        </Col>
      <div className="card-body">
        <div style={{ height: '500px', width: '100%' }}>
          <AgGridReact
            ref={gridRef}
            theme={themeBalham}
            rowData={orderData}
            columnDefs={[
              {
                headerName: 'Status',
                field: 'status',
                sortable: true,
                filter: true,
                cellRenderer: (params) => {
                  return <Tag
                    size="small"
                    style={{height: '90%'}}
                    color={
                      params.value === 'Sales Order : Pending Approval' ? 'red' :
                        params.value === 'Sales Order : Pending Fulfillment' ? 'green' :
                          params.value === 'Sales Order : Pending Billing/Partially Fulfilled' ? 'orange' :
                            params.value === 'Sales Order : Partially Fulfilled' ? 'orange' :
                              'default'
                    }>{params.value.replace('Sales Order : ', '')}</Tag>;
                }
              },
              {
                headerName: 'Document #',
                field: 'document_number',
                sortable: true,
                filter: true,
                cellRenderer: (params) => {
                  if (!params.data || !params.data.id) return params.value;
                  const url = `https://6810379.app.netsuite.com/app/accounting/transactions/salesord.nl?id=${params.data.id}`;
                  return (
                    <a
                      href={url}
                      target="_blank"
                      rel="noopener noreferrer"
                      style={{ color: '#1890ff', textDecoration: 'underline', cursor: 'pointer' }}
                      onClick={e => e.stopPropagation()}
                    >
                      {params.value}
                    </a>
                  );
                }
              },
              {
                headerName: 'Customer',
                field: 'customer',
                sortable: true,
                filter: true,
                enableRowGroup: true,
                valueGetter: params => {
                  if (!params.data) return params.value;
                  return typeof params.data.customer === 'object'
                    ? params.data.customer?.name || ''
                    : params.data.customer || '';
                }
              },
              {
                headerName: 'PO #',
                field: 'po_num',
                sortable: true,
                filter: true,

              },
              {
                headerName: 'Date',
                field: 'date',
                sortable: true,
                filter: true
              },
              {
                headerName: 'Ship Date',
                field: 'dispatch_date',
                sortable: true,
                filter: true
              },
              {
                headerName: 'Committed/Shipped/Total Qty',
                field: 'total_units',
                sortable: true,
                filter: true,
                aggFunc: 'sum',
                valueFormatter: (params) => {
                  const committed = params.data?.total_units_committed ?? '';
                  const shipped = params.data?.total_units_shipped ?? '';
                  const total = params.value ?? '';
                  return `${committed}/${shipped}/${total}`;
                }
              },
              {
                headerName: 'Total Dollars',
                field: 'total_dollars',
                sortable: true,
                filter: true,
                aggFunc: 'sum',
                valueFormatter: (params) => {
                  return (params.value || 0)?.toLocaleString('en-US', {
                    style: 'currency',
                    currency: 'USD'
                  });
                }
              },
              // {
              //   headerName: 'Actions',
              //   field: 'actions',
              //   cellRenderer: (params) => {
              //     if (!params.data || !params.data.id) return null;
              //     const url = `https://6810379.app.netsuite.com/app/accounting/transactions/salesord.nl?id=${params.data.id}`;
              //     if (params.data.status === 'Sales Order : Pending Approval') {
              //       return (
              //         <Button
              //           type="primary"
              //           size="small"
              //           href={url}
              //           target="_blank"
              //           rel="noopener noreferrer"
              //           onClick={e => changeOrderStatuses([params.data.id], 'B')}
              //           style={{ marginRight: 8 }}
              //         >
              //           Approve
              //         </Button>
              //       );
              //     }
              //     if (params.data.status === 'Sales Order : Pending Fulfillment') {
              //       return (
              //         <Button
              //           type="default"
              //           size="small"
              //           href={url}
              //           target="_blank"
              //           rel="noopener noreferrer"
              //           onClick={e => changeOrderStatuses([params.data.id], 'A')}
              //           style={{ marginRight: 8 }}
              //         >
              //           Unapprove
              //         </Button>
              //       );
              //     }
              //     return null;
              //   },
              //   suppressMenu: true,
              //   width: 130,
              //   pinned: false
              // }
            ]}
            onRowClicked={(params) => setModalItem(params.data)}
            domLayout='autoHeight'
            getRowClass={(params) => {
              if (!params.data || !params.data.dispatch_date) return '';
              const today = dayjs().startOf('day');
              const shipDate = dayjs(params.data.dispatch_date);
              if (shipDate.isBefore(today, 'day')) {
                return 'row-ship-past';
              } else if (shipDate.isBefore(today.add(7, 'day'), 'day')) {
                return 'row-ship-soon';
              }
              return '';
            }}
          />
        </div>
      </div>
      {modalItem.id && <OrderDetailsModal modalItem={modalItem} setModalItem={setModalItem} />}
    </div>
  );
};
export default OrdersTab; 