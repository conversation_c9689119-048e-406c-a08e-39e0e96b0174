/* eslint-disable prefer-const */
/* eslint-disable camelcase */
const NsApiWrapper = require("netsuite-rest");
const SuiteQL = require("suiteql");

const axios = require("axios");
const crypto = require("crypto");
const { sendGChatMessage } = require("./google");
const sleep = (ms) => new Promise((resolve) => setTimeout(resolve, ms));
const csv = require("csv-parser");
const { Transform } = require("stream");
const logger = require("firebase-functions/logger");
const { modifyDocs, modifyDoc, queryDocs, deleteDocs } = require("./firestore");
const { defineString } = require("firebase-functions/params");

console.log("process.env.FUNCTIONS_EMULATOR", process.env.FUNCTIONS_EMULATOR);
const EMULATOR_ENV = process.env.FUNCTIONS_EMULATOR ? true : false;

// Define parameters without calling .value() during deployment
const NETSUITE_ACCOUNT_ID_TEST = defineString("NETSUITE_ACCOUNT_ID_TEST");
const NETSUITE_ACCOUNT_ID_PROD = defineString("NETSUITE_ACCOUNT_ID_PROD");
const NETSUITE_CONSUMER_KEY_TEST = defineString("NETSUITE_CONSUMER_KEY_TEST");
const NETSUITE_CONSUMER_KEY_PROD = defineString("NETSUITE_CONSUMER_KEY_PROD");
const NETSUITE_CONSUMER_SECRET_TEST = defineString("NETSUITE_CONSUMER_SECRET_TEST");
const NETSUITE_CONSUMER_SECRET_PROD = defineString("NETSUITE_CONSUMER_SECRET_PROD");
const NETSUITE_TOKEN_TEST = defineString("NETSUITE_TOKEN_TEST");
const NETSUITE_TOKEN_PROD = defineString("NETSUITE_TOKEN_PROD");
const NETSUITE_TOKEN_SECRET_TEST = defineString("NETSUITE_TOKEN_SECRET_TEST");
const NETSUITE_TOKEN_SECRET_PROD = defineString("NETSUITE_TOKEN_SECRET_PROD");

// Function to get runtime values
const getNetSuiteCredentials = () => {
  //*show this when overriding the emulator to get prod data
  // const EMULATOR_ENV = tue;
  const credentials = {
    NETSUITE_ACCOUNT_ID: EMULATOR_ENV ? NETSUITE_ACCOUNT_ID_TEST.value() : NETSUITE_ACCOUNT_ID_PROD.value(),
    NETSUITE_CONSUMER_KEY: EMULATOR_ENV ? NETSUITE_CONSUMER_KEY_TEST.value() : NETSUITE_CONSUMER_KEY_PROD.value(),
    NETSUITE_CONSUMER_SECRET: EMULATOR_ENV ? NETSUITE_CONSUMER_SECRET_TEST.value() : NETSUITE_CONSUMER_SECRET_PROD.value(),
    NETSUITE_TOKEN: EMULATOR_ENV ? NETSUITE_TOKEN_TEST.value() : NETSUITE_TOKEN_PROD.value(),
    NETSUITE_TOKEN_SECRET: EMULATOR_ENV ? NETSUITE_TOKEN_SECRET_TEST.value() : NETSUITE_TOKEN_SECRET_PROD.value(),
  };
  credentials.NETSUITE_BASE_URL = `https://${credentials.NETSUITE_ACCOUNT_ID.replace(
    "_SB1",
    "-sb1",
  )}.suitetalk.api.netsuite.com`;
  credentials.RESTLET_URL = `https://${credentials.NETSUITE_ACCOUNT_ID.replace(
    "_SB1",
    "-sb1",
  )}.restlets.api.netsuite.com/app/site/hosting/restlet.nl`;
  return credentials;
};

// Initialize NetSuite API at runtime
const initializeNetSuiteAPI = () => {
  const credentials = getNetSuiteCredentials();


  return new NsApiWrapper({
    consumer_key: credentials.NETSUITE_CONSUMER_KEY,
    consumer_secret_key: credentials.NETSUITE_CONSUMER_SECRET,
    token: credentials.NETSUITE_TOKEN,
    token_secret: credentials.NETSUITE_TOKEN_SECRET,
    realm: credentials.NETSUITE_ACCOUNT_ID,
    // ,base_url: 'base_url' // optional
  });
};

const genNonce = () => {
  // eslint-disable-next-line max-len
  const charset =
    "0123456789ABCDEFGHIJKLMNOPQRSTUVXYZabcdefghijklmnopqrstuvwxyz";
  const result = [];
  crypto
    .getRandomValues(new Uint8Array(16))
    .forEach((c) => result.push(charset[c % charset.length]));
  return result.join("");
};
const makeNSRequest = async ({
  req_method: method,
  netsuite_account_id: account_id,
  oauth_consumer_key,
  oauth_consumer_secret,
  oauth_token,
  oauth_token_secret,
  endpoint = "",
  params = {},
  data = null,
}) => {
  //   const oauth_nonce = "SucbbWD0SBT";
  let headers = {};
  if (endpoint.includes("metadata-catalog")) {
    headers = {
      Accept: "application/schema+json",
    };
  }
  if (endpoint.includes("suiteql")) {
    headers = {
      "prefer": "transient",
      "X-NetSuite-PropertyNameValidation": "Error",
      "X-NetSuite-PropertyValueValidation": "Error",
    };
  }
  if (endpoint.includes("record")) {
    headers = {
      "User-Agent": "Mozilla/5.0",
      "Accept": "*/*",
      "Accept-Encoding": "gzip, deflate, br",
      "Connection": "keep-alive",
    };
    data = JSON.stringify(data);
  }
  const oauth_nonce = genNonce();
  //   console.log(oauth_nonce)
  const oauth_timestamp = Math.floor(Date.now() / 1000);
  //   const oauth_timestamp = '**********'
  const oauth_version = "1.0";
  const url = `https://${account_id.replace(
    "_SB",
    "-sb",
  )}.suitetalk.api.netsuite.com/services/rest/${endpoint}`;
  console.log(url);
  const oauth_signature_method = "HMAC-SHA256";
  const param_obj = {
    ...params,
    oauth_consumer_key,
    oauth_nonce,
    oauth_signature_method,
    oauth_timestamp,
    oauth_token,
    oauth_version,
  };
  const normalized_params = Object.keys(param_obj)
    .sort()
    .map((key) => {
      return `${encodeURIComponent(key)}=${encodeURIComponent(param_obj[key])}`;
    })
    .join("&");
  const param_string =
    Object.keys(params).length > 0 ?
      "?" +
      Object.keys(params)
        .map((key) => `${key}=${params[key]}`)
        .join("&") :
      "";
  const oauth_base_string = `${method}&${encodeURIComponent(
    url,
  )}&${encodeURIComponent(normalized_params)}`;
  const oauth_signature = encodeURIComponent(
    crypto
      .createHmac(
        "sha256",
        `${encodeURIComponent(oauth_consumer_secret)}&${encodeURIComponent(
          oauth_token_secret,
        )}`,
      )
      .update(oauth_base_string)
      .digest("base64"),
  );
  const header_obj = {
    realm: account_id,
    oauth_consumer_key,
    oauth_token,
    oauth_signature_method,
    oauth_timestamp,
    oauth_nonce,
    oauth_version,
    oauth_signature,
    // ...headers,
  };
  const auth_header = `OAuth ${Object.keys(header_obj)
    .map((key) => `${key}="${header_obj[key]}"`)
    .join(",")}`;
  const config = {
    method: method.toLowerCase(),
    // maxBodyLength: Infinity,
    url: url + param_string,
    headers: {
      // "Prefer": "transient",
      "Content-Type": "application/json",
      "Authorization": auth_header,
      ...headers,
    },
    data,
  };
  // console.log(config);
  let req;
  // console.log("Sending Request to Netsuite", JSON.stringify(config));
  // console.log("Sending Request to Netsuite", JSON.stringify(config));
  try {
    req = await axios.request(config);
  } catch (el) {
    console.error(`Error with axios req`, config, JSON.stringify(el.response.data["o:errorDetails"][0].detail));
    if (el.response.status === 429) {
      console.log("Rate Limit Exceeded, waiting for 500ms");
      await sleep(1000);
      req = await makeNSRequest({
        req_method: method,
        netsuite_account_id: account_id,
        oauth_consumer_key,
        oauth_consumer_secret,
        oauth_token,
        oauth_token_secret,
        endpoint,
        params,
        data,
      });
    }
    return false;
  }
  // console.log("Netsuite Request completed successfully");
  return req.data;
};
const makeRestletRequest = async ({
  req_method: method,
  params = {},
  data = null,
}) => {
  //   const oauth_nonce = "SucbbWD0SBT";
  const credentials = getNetSuiteCredentials();
  let headers = {
    "User-Agent": "Mozilla/5.0",
    "Accept": "*/*",
    "Accept-Encoding": "gzip, deflate, br",
    "Connection": "keep-alive",
  };
  data = JSON.stringify(data);

  const oauth_nonce = genNonce();
  //   console.log(oauth_nonce)
  const oauth_timestamp = Math.floor(Date.now() / 1000);
  //   const oauth_timestamp = '**********'
  const oauth_version = "1.0";
  const oauth_signature_method = "HMAC-SHA256";
  const param_obj = {
    ...params,
    oauth_consumer_key: credentials.NETSUITE_CONSUMER_KEY,
    oauth_nonce,
    oauth_signature_method,
    oauth_timestamp,
    oauth_token: credentials.NETSUITE_TOKEN,
    oauth_version,
  };
  const normalized_params = Object.keys(param_obj)
    .sort()
    .map((key) => {
      return `${encodeURIComponent(key)}=${encodeURIComponent(param_obj[key])}`;
    })
    .join("&");
  const param_string =
    Object.keys(params).length > 0 ?
      "?" +
      Object.keys(params)
        .map((key) => `${key}=${params[key]}`)
        .join("&") :
      "";
  const oauth_base_string = `${method}&${encodeURIComponent(
    credentials.RESTLET_URL,
  )}&${encodeURIComponent(normalized_params)}`;
  const oauth_signature = encodeURIComponent(
    crypto
      .createHmac(
        "sha256",
        `${encodeURIComponent(credentials.NETSUITE_CONSUMER_SECRET)}&${encodeURIComponent(
          credentials.NETSUITE_TOKEN_SECRET,
        )}`,
      )
      .update(oauth_base_string)
      .digest("base64"),
  );
  const header_obj = {
    realm: credentials.NETSUITE_ACCOUNT_ID,
    oauth_consumer_key: credentials.NETSUITE_CONSUMER_KEY,
    oauth_token: credentials.NETSUITE_TOKEN,
    oauth_signature_method,
    oauth_timestamp,
    oauth_nonce,
    oauth_version,
    oauth_signature,
    // ...headers,
  };
  const auth_header = `OAuth ${Object.keys(header_obj)
    .map((key) => `${key}="${header_obj[key]}"`)
    .join(",")}`;
  const config = {
    method: method.toLowerCase(),
    // maxBodyLength: Infinity,
    url: credentials.RESTLET_URL + param_string,
    headers: {
      // "Prefer": "transient",
      "Content-Type": "application/json",
      "Authorization": auth_header,
      ...headers,
    },
    data,
  };
  // console.log(config);
  let req;
  // console.log("Sending Request to Netsuite", JSON.stringify(config));
  // console.log("Sending Request to Netsuite", JSON.stringify(config));
  try {
    req = await axios.request(config);
  } catch (el) {
    console.error(`Error with axios req`, el.response?.data);
    if (el.response.status === 429) {
      console.log("Restlet Rate Limit Exceeded, waiting for 1000ms");
      await sleep(1000);
      req = await makeRestletRequest({
        req_method: method,
        params,
        data,
      });
    }
    return false;
  }
  // console.log("Netsuite Request completed successfully");
  return req.data;
};


const createNetSuiteRecord = async (recordType, data) => {
  try {
    const nsApi = initializeNetSuiteAPI();
    let endpoint = `record/v1/${recordType}`;
    // const params = {};
    const headers = {
      "User-Agent": "Mozilla/5.0",
      "Accept": "*/*",
      "Accept-Encoding": "gzip, deflate, br",
      "Connection": "keep-alive",
    };
    const method = "POST";
    data = JSON.stringify(data);
    const createResp = await nsApi.request({
      path: endpoint,
      method,
      body: data,
      headers,
    });
    console.log("createResp", createResp);
    if (createResp.statusCode !== 204) {
      throw new Error(`Create request failed: ${createResp.statusCode} ${createResp.statusMessage}`);
    }
    return { status: true, id: createResp.data };
  } catch (error) {
    console.error(`Error creating record ${data}:`, error.response?.body || error.message);
    if (error.response?.body?.includes("This record already exists")) {
      return { status: true, id: null };
    }
    if (error.response?.body?.includes("Please configure the inventory detail for")) {
      return {
        status: false,
        msg: error.response?.body,
        errType: "inventoryDetail",
      };
    }
    if (error.response?.body?.includes("Concurrent request limit exceeded")) {
      // retry error
      console.log("Rate Limit Exceeded, waiting for 3000ms");
      await sleep(3000);
      return await createNetSuiteRecord(recordType, data);
    }
    if (error.code === "ETDTIMEOUT" || (error.response?.statusCode === 429)) {
      // retry error
      console.log("Rate Limit Exceeded, waiting for 3000ms");
      await sleep(3000);
      return await createNetSuiteRecord(recordType, data);
    }
    return { status: false, msg: error.response?.body || error.message, stack: error.stack };
  }
};
const getNetSuiteRecord = async (recordType, id, isExternalId = false, expandSubResources = true, simpleEnumFormat = true) => {
  try {
    const nsApi = initializeNetSuiteAPI();

    let endpoint = `record/v1/${recordType}`;
    const params = {};
    if (isExternalId) {
      if (id.includes(":")) {
        params.q = `externalId IS "${id}"`;
      } else {
        endpoint += `/eid:${id}`;
      }
    } else {
      endpoint += `/${id}`;
    }
    if (expandSubResources) {
      params.expandSubResources = true;
    }
    if (simpleEnumFormat) {
      params.simpleEnumFormat = true;
    }
    endpoint += `?${new URLSearchParams(params).toString()}`;
    const result = await nsApi.request({
      path: endpoint,
      method: "GET",
    });
    return result.data;
  } catch (error) {
    console.error(`Error getting record ${recordType}:${id}`, error.response?.body || error.message);
    return { status: false, msg: error.response?.body || error.message };
  }
};
const queryNetSuite = async ({ q, paginate = true, offset = 0, connector }) => {
  const credentials = getNetSuiteCredentials();
  q = q.replaceAll(/"/g, "\\\"");
  q = q.replaceAll("\n", "");
  console.log("q", q);
  const items = [];
  let hasMore = false;
  let config = {
    req_method: "POST",
    netsuite_account_id: credentials.NETSUITE_ACCOUNT_ID,
    oauth_consumer_key: credentials.NETSUITE_CONSUMER_KEY,
    oauth_consumer_secret: credentials.NETSUITE_CONSUMER_SECRET,
    oauth_token: credentials.NETSUITE_TOKEN,
    oauth_token_secret: credentials.NETSUITE_TOKEN_SECRET,
    endpoint: "query/v1/suiteql",
    data: {
      q,
    },
  };
  try {
    do {
      try {
        const dupResp = await makeNSRequest(config);
        if (!dupResp || !dupResp.items) {
          console.error("Error fetching queryNetSuite", dupResp);
          break;
        }
        if (dupResp.items.length === 0) {
          hasMore = false;
          break;
        }
        items.push(...dupResp.items);
        if (!dupResp.links) {
          hasMore = false;
          break;
        }
        const nextLink = dupResp.links.find((link) => link.rel === "next");
        if (!nextLink) {
          hasMore = false;
          break;
        }
        const path = nextLink.href.split("/rest/")[1];
        const params = path
          .split("?")[1]
          .split("&")
          .reduce((acc, cur) => {
            const [key, val] = cur.split("=");
            acc[key] = val;
            return acc;
          }, {});
        config.endpoint = path.split("?")[0];
        config.params = { ...config.params, ...params };
        hasMore = true;
      } catch (error) {
        if (error.response && error.response.status === 429) {
          console.log("Rate Limit Exceeded, waiting for 500ms");
          await sleep(1000);
          continue;
        }
      }
    } while (paginate && hasMore);
  } catch (error) {
    console.error(`Error fetching queryNetSuite`, {
      stack: error.stack,
      details: JSON.stringify({ msg: error.message, stack: error.stack }),
    });
    return {
      error: error.message,
    };
  }
  return items;
};
const netsuiteSuiteQlQuery = async ({ q, limit = 1000, offset = 0, connector, dateParams = {} }) => {
  let suiteQL = new SuiteQL({
    consumer_key: connector.connection.consumerKey,
    consumer_secret_key: connector.connection.consumerSecret,
    token: connector.connection.tokenKey,
    token_secret: connector.connection.tokenSecret,
    realm: connector.connection.realm,
    // base_url: netsuiteBaseURL
  });

  // Replace BUILTIN.DF() with a supported alternative
  // The BUILTIN.DF() function is not supported in SuiteQL
  let modifiedQuery = q;
  if (q.includes("BUILTIN.DF")) {
    // Replace BUILTIN.DF(column) with just the column name
    modifiedQuery = q.replace(/BUILTIN\.DF\(([^)]+)\)/g, "$1");
    console.log("Modified query to remove BUILTIN.DF:", modifiedQuery);
  }

  // Replace date parameters if provided
  if (dateParams.startTime) {
    modifiedQuery = modifiedQuery.replace(/{{startTime}}/g, `'${dateParams.startTime}'`);
  }
  if (dateParams.endTime) {
    modifiedQuery = modifiedQuery.replace(/{{endTime}}/g, `'${dateParams.endTime}'`);
  }

  // Clean up the query for SuiteQL
  // 1. Remove comments (they can cause parsing issues)
  modifiedQuery = modifiedQuery.replace(/--.*$/gm, "");

  // 2. Replace newlines with spaces and clean up multiple spaces
  const formattedQuery = modifiedQuery.replace(/\s+/g, " ").trim();

  console.log("Final formatted query:", formattedQuery);

  try {
    let results = await suiteQL.query(formattedQuery, Math.min(limit, 1000), offset);
    return { results: results.items, offset: offset + Math.min(limit, results.items.length), hasMore: results.hasMore };
  } catch (error) {
    console.error("Error in netsuiteSuiteQlQuery:", error);
    throw error;
  }
};

const updateNetSuiteRecord = async ({ recordType, internalId, data }) => {
  try {
    const nsApi = initializeNetSuiteAPI();
    const endpoint = `record/v1/${recordType}/${internalId}`;
    // const params = {};
    const method = "PATCH";
    const headers = {
      "User-Agent": "Mozilla/5.0",
      "Accept": "*/*",
      "Accept-Encoding": "gzip, deflate, br",
      "Connection": "keep-alive",
      "X-NetSuite-PropertyNameValidation": true,
      "X-NetSuite-PropertyValueValidation": true,
    };
    const body = JSON.stringify(data);
    const updateResp = await nsApi.request({
      path: endpoint,
      method,
      body,
      headers,
    });
    console.log("updateResp", updateResp.statusCode);
    if (updateResp.statusCode !== 204) {
      throw new Error(`Update request failed: ${updateResp.statusCode} ${updateResp.statusMessage}`);
    }
    return updateResp.data;
  } catch (error) {
    console.error(
      `Error updating record ${recordType} ${internalId}:`,
      error.response ? error.response?.body : error.message,
    );
    return { status: false, msg: error.response?.body || error.message, stack: error.stack };
  }
};

const deleteNetSuiteRecord = async (recordType, internalId) => {
  try {
    const credentials = getNetSuiteCredentials();
    const url = `${credentials.NETSUITE_BASE_URL}/services/rest/record/v1/${recordType}`;
    let headers = {};
    const params = {};
    const method = "DELETE";
    headers = {
      "User-Agent": "Mozilla/5.0",
      "Accept": "*/*",
      "Accept-Encoding": "gzip, deflate, br",
      "Connection": "keep-alive",
    };
    const oauth_nonce = genNonce();
    //   console.log(oauth_nonce)
    const oauth_timestamp = Math.floor(Date.now() / 1000);
    //   const oauth_timestamp = '**********'
    const oauth_version = "1.0";
    console.log(url);
    const oauth_signature_method = "HMAC-SHA256";
    const param_obj = {
      ...params,
      oauth_consumer_key: credentials.NETSUITE_CONSUMER_KEY,
      oauth_nonce,
      oauth_signature_method,
      oauth_timestamp,
      oauth_token: credentials.NETSUITE_TOKEN,
      oauth_version,
    };
    const normalized_params = Object.keys(param_obj)
      .sort()
      .map((key) => {
        return `${encodeURIComponent(key)}=${encodeURIComponent(
          param_obj[key],
        )}`;
      })
      .join("&");
    const param_string =
      Object.keys(params).length > 0 ?
        "?" +
        Object.keys(params)
          .map((key) => `${key}=${params[key]}`)
          .join("&") :
        "";
    const oauth_base_string = `${method}&${encodeURIComponent(
      url,
    )}&${encodeURIComponent(normalized_params)}`;
    const oauth_signature = encodeURIComponent(
      crypto
        .createHmac(
          "sha256",
          `${encodeURIComponent(credentials.NETSUITE_CONSUMER_SECRET)}&${encodeURIComponent(
            credentials.NETSUITE_TOKEN_SECRET,
          )}`,
        )
        .update(oauth_base_string)
        .digest("base64"),
    );
    const header_obj = {
      realm: credentials.NETSUITE_ACCOUNT_ID,
      oauth_consumer_key: credentials.NETSUITE_CONSUMER_KEY,
      oauth_token: credentials.NETSUITE_TOKEN,
      oauth_signature_method,
      oauth_timestamp,
      oauth_nonce,
      oauth_version,
      oauth_signature,
      // ...headers,
    };
    const auth_header = `OAuth ${Object.keys(header_obj)
      .map((key) => `${key}="${header_obj[key]}"`)
      .join(",")}`;
    const config = {
      method: method.toLowerCase(),
      // maxBodyLength: Infinity,
      url: url + param_string,
      headers: {
        // "Prefer": "transient",
        "Content-Type": "application/json",
        "Authorization": auth_header,
        ...headers,
      },
    };
    let req;
    // console.log("Sending Request to Netsuite", JSON.stringify(config));
    try {
      req = await axios.request(config);
      console.log(`Record ${recordType} created successfully:`, req.data);
      return { status: true, id: req.data };
    } catch (el) {
      console.error(
        `Error with axios req`,
        config,
        JSON.stringify(el.response.data),
      );
      console.error(
        "el.response.data['o:errorDetails'][0].detail",
        el.response.data["o:errorDetails"][0].detail,
      );
      if (
        el.response.data["o:errorDetails"][0].detail.includes(
          "This record already exists.",
        )
      ) {
        return { status: true, msg: el.response.data };
      }
      if (
        el.response.data["o:errorDetails"][0].detail.includes(
          "Please configure the inventory detail for",
        )
      ) {
        // create inventory adjustment
        return {
          status: false,
          msg: el.response.data,
          errType: "inventoryDetail",
        };
      }
      return { status: false, msg: el.response.data };
    }
  } catch (error) {
    console.error(
      `Error deleting record ${recordType} ${internalId}:`,
      error.message,
    );
    return { status: false, msg: error.response.data };
  }
};

const getNetSuiteSavedSearch = async ({ connector, searchId, filterField, filterValue, maxResults = 5000 }) => {
  const res = await makeRestletRequest({
    req_method: "POST",
    params: { script: "3860", deploy: "1" },
    data: {
      searchId,
      filterField,
      lastFilterFieldValue: filterValue,
      maxResults,
      reqType: "filtered"
    },
  });

  return res.searchResults;
};

const cancelOrder = async (orderId) => {
  try {
    sendGChatMessage("ERRORS", `Cancelling order ${orderId} in NetSuite`);
    const res = await makeRestletRequest({
      req_method: "POST",
      params: { script: "3665", deploy: "1" },
      data: {
        orderId,
        custbody_is_held: true,
      },
    });
    return res;
  } catch (error) {
    console.error(`Error cancelling order ${orderId}:`, error.message);
    return false;
  }
};

const parseCSV = (data) => {
  return new Promise((resolve, reject) => {
    const results = [];
    const stream = new Transform({
      readableObjectMode: true,
      transform(chunk, encoding, callback) {
        callback(null, chunk);
      },
    });

    stream
      .pipe(csv())
      .on("data", (row) => {
        // Exclude records where UPC Code is "Total"
        if (row["UPC Code"] !== "Total") {
          results.push(row);
        }
      })
      .on("end", () => {
        resolve(results);
      })
      .on("error", (error) => {
        reject(error);
      });

    stream.write(data);
    stream.end();
  });
};

const makeNSSavedSearchRequest = async (searchId) => {
  try {
    const baseUrl = "https://6810379.extforms.netsuite.com/app/site/hosting/scriptlet.nl";
    const params = {
      "script": "1873",
      "deploy": "1",
      "compid": "6810379",
      "ns-at": "AAEJ7tMQ62yb0CjU7hRc7bEjjwQw9nJDo95m1agKif4C71WED_8",
      "key": "Hydr04L1fe!",
      "searchId": searchId,
    };

    // Build query string
    const queryString = Object.entries(params)
      .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
      .join("&");

    const url = `${baseUrl}?${queryString}`;

    const response = await axios.get(url, { responseType: "text" });
    const csvData = response.data;

    // Parse CSV data using the internal parseCSV function
    const results = await parseCSV(csvData);

    return results;
  } catch (error) {
    console.error(`Error executing saved search ${searchId}:`, error);
    logger.error(`Error executing saved search ${searchId}:`, error);
    throw error;
  }
};

// TODO convert to use queryNetSuite
// eslint-disable-next-line max-len
const NETSUITE_SUITEQL_URL = "https://6810379.extforms.netsuite.com/app/site/hosting/scriptlet.nl?script=2429&deploy=1&compid=6810379&ns-at=AAEJ7tMQGlmRGHcY_SD2AuE0pwyKFLbE8RqIf_GF6RFIWrnEqUw";
const NETSUITE_SUITEQL_KEY = "Hyrdo4L1fe!";

const executeNSSuiteQLQuery = async (query, rowBegin = 0, rowEnd = 5000) => {
  try {
    logger.info("Executing SuiteQL Query:", query);

    const headersList = {
      "Accept": "*/*",
      "User-Agent": "Mozilla/5.0",
      "Content-Type": "application/json",
      "Key": NETSUITE_SUITEQL_KEY,
    };

    const bodyContent = JSON.stringify({
      rowEnd: rowEnd,
      rowBegin: rowBegin,
      q: query,
    });

    logger.info("Request Body:", bodyContent);

    const reqOptions = {
      url: NETSUITE_SUITEQL_URL,
      method: "POST",
      headers: headersList,
      data: bodyContent,
      timeout: 530000, // 530 seconds timeout
    };

    logger.info("Sending request to NetSuite API");
    const response = await axios.request(reqOptions);
    logger.info("Received response from NetSuite API");
    logger.info("Response status:", response.status);
    logger.info("Response headers:", response.headers);

    // Log response structure
    logger.info("Response data type:", typeof response.data);
    logger.info("Response data keys:", Object.keys(response.data));

    return response.data;
  } catch (error) {
    logger.error("Error executing SuiteQL query:", error);
    logger.error("Error response:", error.response ? error.response.data : "No response data");
    throw error;
  }
};

//*TODO finish testing this
const netsuiteTriggerTransactionExport = async (periodName) => {
  try {
    const res = await makeRestletRequest({
      req_method: "POST",
      params: { script: "3949", deploy: "1" },
      data: {
        periodName,
      },
    });
    return res;
  } catch (error) {
    console.error(`Error triggering transaction export for period ${periodName}:`, error.message);
    return false;
  }
};

const refreshOpenOrders = async ({ filters }) => {
  const { upc, docNumber, poNumber, customer, dateRangeFilter } = filters;
  console.log("fetching open orders for", filters);

  // Build where conditions for main transaction query
  const transactionWhereConditions = [
    // `t.id=186599418`,
    `t.type = 'SalesOrd'`,
    `t.cseg_class2=1`,
    `ts.name NOT IN ('Closed','Billed','Pending Billing','Cancelled')`,
  ];

  if (docNumber) {
    transactionWhereConditions.push(`t.tranid LIKE '%${docNumber}%'`);
  }
  if (poNumber) {
    transactionWhereConditions.push(`t.otherrefnum LIKE '%${poNumber}%'`);
  }
  if (customer) {
    transactionWhereConditions.push(`cust.id = '${customer}'`);
  }

  try {
    // Query 1: Get main transaction data
    const transactionQuery = `SELECT
      t.id AS transactionid,
      TO_CHAR(t.trandate, 'YYYY-MM-DD') AS orderdate,
      t.tranid AS docnumber,
      t.otherrefnum AS ponumber,
      ts.name as status,
      cust.altname AS customername,
      t.shipdate as orgcancel,
      t.custbody_forecast_node as forecastnode
      FROM transaction AS t
      JOIN entity AS cust ON t.entity = cust.id
      JOIN transactionstatus as ts on t.status = ts.id and t.type=ts.trantype
      WHERE ${transactionWhereConditions.join(" AND ")}`;

    console.log("Transaction query:", transactionQuery);
    const transactions = await queryNetSuite({ q: transactionQuery, paginate: true });
    console.log("Transactions:", transactions.length);

    if (!transactions || transactions.length === 0) {
      console.log("No transactions found matching criteria");
      return [];
    }

    // Get transaction IDs for line query
    const transactionIds = transactions.map(t => t.transactionid);
    const transactionIdList = transactionIds.map(id => `${id}`).join(",");
    // Build where conditions for transaction lines query
    const lineWhereConditions = [
      `l.transaction IN(${transactionIdList})`,
      `i.itemtype='InvtPart'`,
      `i.upccode is not null`,
      `l.isClosed='F'`,
      `l.isFullyShipped='F'`,
      `(l.quantity+l.quantitybilled) < 0`,
    ];

    if (upc) {
      lineWhereConditions.push(`i.upccode = '${upc}'`);
    }
    if (dateRangeFilter && dateRangeFilter.length > 0) {
      const startDate = dateRangeFilter[0];
      const endDate = dateRangeFilter[1];
      if (startDate && endDate) {
        lineWhereConditions.push(`l.requesteddate >= '${startDate}' AND l.requesteddate <= '${endDate}'`);
      } else if (startDate) {
        lineWhereConditions.push(`l.requesteddate >= '${startDate}'`);
      } else if (endDate) {
        lineWhereConditions.push(`l.requesteddate <= '${endDate}'`);
      }
    }

    // Query 2: Get transaction lines with item details
    const lineQuery = `SELECT
      l.uniquekey as lineid,
      l.lineSequenceNumber as linenumber,
      l.transaction as transactionid,
      i.upccode as upc,
      l.quantity as quantityordered,
      l.quantityCommitted as quantitycommitted,
      l.quantityShipRecv as quantityshipped,
      l.quantityPicked as quantitypicked,
      l.quantitybilled as quantitybilled,
      l.expectedShipDate as expectedshipdate,
      COALESCE(oas.name, '- Do Not Allocate -') as allocationstrategy,
      COALESCE(oas.id, 0) as allocationstrategyid,
      l.requesteddate as canceldate,
      l.custcol_prep_date as prepdate,
      l.custcol_start_ship as startdate,
      i.description as itemdesc,
      i.id as item_id,
      BUILTIN.DF(i.custitem20) as lifestatus,
      BUILTIN.DF(i.custitem_color) as color,
      BUILTIN.DF(i.custitem_size) as size,
      BUILTIN.DF(i.custitem64) as division,
      BUILTIN.DF(i.custitem78) as category,
      BUILTIN.DF(i.custitem80) as form,
      BUILTIN.DF(i.custitem76) as family,
      BUILTIN.DF(i.custitem2) as specification,
      i.custitem24 as launchdate,
      i.custitem25 as enddate,
      i.custitem77 as obsoletedate,
      l.rate as rate,
      l.foreignAmount as amount,
      l.isClosed as isclosed
      FROM transactionline as l
      JOIN item AS i ON l.item = i.id
      LEFT JOIN orderAllocationStrategy as oas on l.orderallocationstrategy = oas.id
      WHERE ${lineWhereConditions.join(" AND ")}`;

    console.log("Line query:", lineQuery);
    const lines = await queryNetSuite({ q: lineQuery, paginate: true });
    console.log("Lines:", lines.length);

    // Process results and determine creates/updates/deletes
    const results = [];
    const transactionMap = new Map();
    const lastUpdated = new Date().toISOString();
    const closedStatuses = ["Cancelled", "Closed", "Billed", "Pending Billing"];

    // Create a map of transactions for quick lookup
    transactions.forEach(transaction => {
      transactionMap.set(transaction.transactionid, transaction);
    });
    lines.forEach(line => {
      const transaction = transactionMap.get(line.transactionid);
      if (transaction) {
        results.push({
          id: line.lineid.toString(),
          transactionid: transaction.transactionid,
          lineid: line.lineid,
          linenumber: line.linenumber,
          orderdate: transaction.orderdate,
          docnumber: transaction.docnumber,
          ponumber: transaction.ponumber,
          status: transaction.status,
          customername: transaction.customername,
          quantityordered: parseInt(line.quantityordered || 0) * -1, // Convert to positive
          quantitycommitted: parseInt(line.quantitycommitted || 0),
          quantitypicked: parseInt(line.quantitypicked || 0),
          quantityshipped: parseInt(line.quantityshipped || 0),
          quantityopen: closedStatuses.includes(transaction.status) ? 0 : parseInt(line.quantityordered * -1 || 0) - parseInt(line.quantityshipped || 0),
          canceldate: line.canceldate ? new Date(line.canceldate) : null,
          prepdate: line.prepdate ? new Date(line.prepdate) : null,
          startdate: line.startdate ? new Date(line.startdate) : null,
          orgcancel: transaction.orgcancel ? new Date(transaction.orgcancel) : null,
          allocationstrategy: line.allocationstrategy,
          allocationstrategyid: line.allocationstrategyid,
          forecastnode: transaction.forecastnode,
          upc: line.upc,
          itemdesc: line.itemdesc,
          item_id: line.item_id,
          amount: parseFloat(line.amount) * -1 || 0,
          rate: parseFloat(line.rate) || 0,
          expectedshipdate: line.expectedshipdate ? new Date(line.expectedshipdate) : null,
          syncStatus: "completed",
          lastUpdated: lastUpdated,
          lifestatus: line.lifestatus,
          color: line.color,
          size: line.size,
          division: line.division,
          category: line.category,
          form: line.form,
          family: line.family,
          specification: line.specification,
          isclosed: line.isclosed === "T"
        });
      }
    });

    let dataToUpdate = results.map(r => ({ id: r.id, data: r }));
    console.log("dataToUpdate", dataToUpdate.length, dataToUpdate.slice(0, 1));
    const existingDocs = await queryDocs("openOrders", []);
    // Delete any existingDocs not in the new NetSuite data, and update/create any docs that have changed or are new
    // Build maps for fast lookup
    const dataToUpdateMap = new Map(dataToUpdate.map(i => [i.id, i.data]));
    const existingDocsMap = new Map(existingDocs.map(doc => [doc.id, doc]));
    // Find docs to delete (in Firestore but not in NetSuite data)
    const idsToDelete = [];
    for (const doc of existingDocs) {
      if (!dataToUpdateMap.has(doc.id)) {
        idsToDelete.push(doc.id);
      }
    }
    console.log("idsToDelete", idsToDelete.length);
    if (idsToDelete.length > 0) {
      await deleteDocs("openOrders", [], idsToDelete);
    }

    // Find docs to update (new or changed)
    const updateDocs = [];
    const keysToExclude = ["lastUpdated"];
    for (const [id, newData] of dataToUpdateMap.entries()) {
      const existingDoc = existingDocsMap.get(id);
      let needsUpdate = false;
      if (!existingDoc) {
        needsUpdate = true;
      } else {
        for (const key in newData) {
          if (keysToExclude.includes(key)) continue;
          const newVal = newData[key];
          const oldVal = existingDoc[key];

          // Handle Date and Firestore Timestamp comparison
          const isFirestoreTimestamp = v => v && typeof v === "object" && (typeof v.toDate === "function" || (typeof v.seconds === "number" && typeof v.nanoseconds === "number"));
          let newDate;
          let oldDate;
          if (newVal instanceof Date || isFirestoreTimestamp(newVal)) {
            newDate = newVal instanceof Date ? newVal : newVal.toDate ? newVal.toDate() : new Date(newVal.seconds * 1000);
          }
          if (oldVal instanceof Date || isFirestoreTimestamp(oldVal)) {
            oldDate = oldVal instanceof Date ? oldVal : oldVal.toDate ? oldVal.toDate() : new Date(oldVal.seconds * 1000);
          }
          if (newDate && oldDate) {
            if (newDate.getTime() !== oldDate.getTime()) {
              needsUpdate = true;
              break;
            }
          } else if (newVal !== oldVal) {
            needsUpdate = true;
            break;
          }
        }
      }
      if (needsUpdate) {
        updateDocs.push({ id, data: newData });
      }
    }
    console.log("updateDocs", updateDocs.length);
    if (updateDocs.length > 0) {
      await modifyDocs("openOrders", updateDocs);
    }
    await modifyDoc("lists", "lastUpdates", { openOrders: new Date(), openOrdersRefreshing: false });
    return results;
  } catch (error) {
    console.error("Error in refreshOpenOrders:", error);
    await modifyDoc("lists", "lastUpdates", { openOrdersRefreshing: false });
    throw error;
  }
};

const updateSalesOrderTask = async ({ orderId, lines, isLastUpdate, taskId }) => {
  console.log("updateSalesOrderTask", orderId, lines);
  await modifyDocs("openOrders", lines.map(d => ({ id: d.lineId.toString(), data: { syncStatus: "processing" } })));
  await modifyDoc("tasks", taskId, { status: "processing" });
  const record = await getNetSuiteRecord("salesorder", orderId);
  if (!record?.item) {
    const errorMessage = `NetSuite record not found or missing items for order ${orderId}`;
    console.error(errorMessage);

    // Update Firestore with error status
    await modifyDocs("openOrders", lines.map(d => ({
      id: d.lineId.toString(),
      data: {
        syncStatus: "error",
        errorMessage: errorMessage
      }
    })));

    await modifyDoc("tasks", taskId, {
      status: "error",
      error: errorMessage
    });

    throw new Error(errorMessage);
  }
  const soItems = record.item.items;
  const nsItems = [];
  lines.forEach(({ lineNumber, allocationStrategy, startDate, canceldate, prepdate, newItemId, rate, price, isclosed }) => {
    console.log("lineNumber", lineNumber);
    const line = soItems.find(l => l.line === parseInt(lineNumber));
    if (line) {
      const updateObj = { line: line.line };
      if (allocationStrategy) {
        const allocationStrategyId = parseInt(allocationStrategy);
        if (allocationStrategyId === 0) {
          updateObj.orderallocationstrategy = null;
        } else {
          updateObj.orderallocationstrategy = { id: allocationStrategyId };
        }
      }
      if (startDate) {
        updateObj.custcol_start_ship = new Date(startDate).toISOString().split("T")[0];
      }
      if (canceldate) {
        updateObj.requesteddate = new Date(canceldate).toISOString().split("T")[0];
      }
      if (prepdate) {
        updateObj.custcol_prep_date = new Date(prepdate).toISOString().split("T")[0];
      }
      if (isclosed) {
        updateObj.isClosed = isclosed;
      }
      if (newItemId) {
        updateObj.item = { id: parseInt(newItemId) };
        updateObj.rate = rate;
        updateObj.price = price;
      }
      nsItems.push(updateObj);
    }
  });
  console.log("updatedItems", nsItems);
  try {
    // Make the actual NetSuite API call
    const netsuiteResult = await updateNetSuiteRecord({
      recordType: "salesorder",
      internalId: orderId,
      data: { item: { items: nsItems } }
    });

    console.log("NetSuite update successful:", netsuiteResult);

    // Update Firestore with success status
    await modifyDocs("openOrders", lines.map(d => ({
      id: d.lineId.toString(),
      data: {
        syncStatus: "completed",
        lastSyncAt: new Date().toISOString(),
        netsuiteConfirmed: true
      }
    })));

    await modifyDoc("tasks", taskId, {
      status: "completed",
      completedAt: new Date().toISOString(),
      netsuiteResult: netsuiteResult
    });

    // Refresh Firestore data to reflect NetSuite changes
    console.log("Refreshing Firestore data after NetSuite update...");
    await refreshOpenOrders({ filters: {} });
  } catch (error) {
    console.error("Error in updateSalesOrderTask:", error);
    // Fix: Set syncStatus to "error" on failure, not "completed"
    await modifyDocs("openOrders", lines.map(d => ({ id: d.lineId.toString(), data: { syncStatus: "error", errorMessage: error.message } })));
    await modifyDoc("tasks", taskId, { status: "error", error: error.message + " " + error.stack });
    throw error;
  }
  return { status: true, orderId, lines };
};

const getNsInventory = async (skus = []) => {
  const credentials = getNetSuiteCredentials();
  let scriptId = 4001;
  if (credentials.NETSUITE_ACCOUNT_ID.includes("SB1")) {
    scriptId = 4003;
  }
  const res = await makeRestletRequest({
    req_method: "POST",
    params: { script: scriptId, deploy: 1 },
    data: { skus },
  });
  return res;
};

// Export the new functions
module.exports = {
  createNetSuiteRecord,
  getNetSuiteRecord,
  queryNetSuite,
  updateNetSuiteRecord,
  deleteNetSuiteRecord,
  cancelOrder,
  makeNSSavedSearchRequest,
  executeNSSuiteQLQuery,
  netsuiteSuiteQlQuery,
  getNetSuiteSavedSearch,
  netsuiteTriggerTransactionExport,
  refreshOpenOrders,
  updateSalesOrderTask,
  getNsInventory,
};