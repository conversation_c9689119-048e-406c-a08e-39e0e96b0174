import React, { useState, useEffect, useMemo, useRef, useCallback } from 'react';
import { DownloadOutlined, ReloadOutlined, InfoCircleOutlined, CloseCircleOutlined, SaveOutlined, DeleteOutlined } from '@ant-design/icons';
import { ShareOutlined } from '@mui/icons-material';
import { message, Descriptions, Table, Select, Modal, Row, Col, Typography, Button, Card, Dropdown, Menu, Tooltip, Popconfirm } from 'antd';
import { AgGridReact } from 'ag-grid-react';
import dayjs from 'dayjs';
import { api, db } from './firebase';
import CopyToClipboardIcon from '../components/CopyIcon';
import { lifeStatusColors, itemClassificationColors, INVENTORY_EXCEPTION_REPORT_URL } from '../constants';
import './InventoryExceptionReport.css';
import { onSnapshot, doc, collection, addDoc, setDoc, deleteDoc, query, where, or } from 'firebase/firestore';
import { getStorage, ref, getDownloadURL, getBlob } from 'firebase/storage';
import { useUser } from '../contexts/UserContext';
import { themeBalham } from 'ag-grid-community';

const { Text } = Typography;

// Simple date helper
const getNextNDates = (n) => Array.from({ length: n }, (_, i) => {
  const date = new Date();
  date.setDate(date.getDate() + i);
  return date.toISOString().split('T')[0];
});

// Debounce utility for performance
const useDebounce = (value, delay) => {
  const [debouncedValue, setDebouncedValue] = useState(value);
  
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);
    
    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);
  
  return debouncedValue;
};

const InventoryExceptionReport = () => {
  const { userData } = useUser();
  const gridRef = useRef();
  const [groupBy, setGroupBy] = useState('product'); // NEW STATE for grouping field
  const debouncedGroupBy = useDebounce(groupBy, 300); // Debounce grouping changes
  const [rowData, setRowData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [detailModalData, setDetailModalData] = useState(null);
  const [modalVisible, setModalVisible] = useState(false);
  const [modalData, setModalData] = useState(null);
  const [legendVisible, setLegendVisible] = useState(false);
  const [nodeColors, setNodeColors] = useState({});
  const [inboundModalVisible, setInboundModalVisible] = useState(false);
  const [inboundModalData, setInboundModalData] = useState(null);
  const [columnDefs, setColumnDefs] = useState([]);
  const [lastUpdate, setLastUpdate] = useState(null);
  const [savedViews, setSavedViews] = useState([]);
  const [selectedViewId, setSelectedViewId] = useState(null);
  const [applyingView, setApplyingView] = useState(false);
  const [groupingLoading, setGroupingLoading] = useState(false);
  const nextDates = useMemo(() => getNextNDates(90).map(date => dayjs(date).format('YYYY-MM-DD')), []);

  // Fetch saved views - user's private views + all public views
  const fetchViews = () => {
    if (!userData?.id) {
      setSavedViews([]);
      return () => {};
    }
    
    // Query to get user's private views + all public views
    const q = query(
      collection(db, 'inventoryExceptionViews'),
      or(
        where('createdBy', '==', userData.id), // User's own views (private)
        where('isPublic', '==', true) // All public views
      )
    );
    
    const unsub = onSnapshot(q, (snap) => {
      setSavedViews(snap.docs.map(doc => ({ id: doc.id, ...doc.data() })));
    });
    return unsub;
  };

  // Load user's last used view
  const loadLastUsedView = async () => {
    if (!userData?.id) return;
    
    const lastViewId = userData.lastInventoryExceptionView;
    
    if (lastViewId) {
      // Check if the view still exists in saved views
      const viewExists = savedViews.some(view => view.id === lastViewId);
      if (viewExists) {
        // Apply the last used view
        setSelectedViewId(lastViewId);
        await applyViewState(lastViewId);
      } else {
        // View no longer exists, clear the reference
        setDoc(doc(db, 'users', userData.id), { 
          lastInventoryExceptionView: null 
        }, { merge: true });
      }
    }
  };

  // Save view function
  const handleSaveView = async () => {
    const name = prompt('Enter a name for the view');
    if (!name || !name.trim()) return;
    
    // Sanitize the name to prevent XSS
    const sanitizedName = name.trim().substring(0, 100);
    
    try {
      if (!gridRef.current?.api) {
        message.error('Grid not ready for saving');
        return;
      }

      const api = gridRef.current.api;
      
      // Capture comprehensive grid state
      let gridState = {};
      try {
        gridState = {
          // Use getState() as primary method, but add fallbacks
          fullState: api.getState(),
          // Also capture individual components as backup
          columnState: api.getColumnState ? api.getColumnState().filter(col => col.colId !== 'ag-Grid-AutoColumn') : [],
          columnGroupState: api.getColumnGroupState ? api.getColumnGroupState() : [],
          filterModel: api.getFilterModel ? api.getFilterModel() : {},
          sortModel: api.getSortModel ? api.getSortModel() : [],
          rowGroupCols: api.getRowGroupColumns ? api.getRowGroupColumns().map(c => c.getColId()) : [],
          pivotCols: api.getPivotColumns ? api.getPivotColumns().map(c => c.getColId()) : [],
          valueCols: api.getValueColumns ? api.getValueColumns().map(c => c.getColId()) : [],
          pivotMode: api.isPivotMode ? api.isPivotMode() : false,
          columnDefs: columnDefs, // Include column definitions for this report
          groupBy: groupBy // Save current group by field
        };
      } catch (gridError) {
        console.warn('Some grid state could not be captured:', gridError);
        // Continue with partial state
        gridState = { fullState: api.getState(), groupBy: groupBy, columnDefs: columnDefs };
      }

      const newDoc = await addDoc(collection(db, 'inventoryExceptionViews'), {
        name: sanitizedName,
        gridState: JSON.parse(JSON.stringify(gridState)),
        createdBy: userData?.id,
        isPublic: false,
        createdByEmail: userData?.email,
        timestamp: Date.now()
      });
      
      // Update the user's preferred view
      await setDoc(doc(db, 'users', userData.id), {
        lastInventoryExceptionView: newDoc.id,
      }, { merge: true });
      
      setSelectedViewId(newDoc.id);
      message.success('View saved successfully');
    } catch (error) {
      console.error('Error saving view:', error);
      message.error('Failed to save view');
    }
  };

  const applyViewState = async (viewId) => {
    if (!gridRef?.current?.api) {
      console.warn('Grid API not ready');
      return;
    }

    const api = gridRef.current.api;
    const selectedView = savedViews.find(view => view.id === viewId);
    
    if (!selectedView || !selectedView.gridState) {
      console.warn('Selected view or grid state not found');
      // Reset to default state
      api.setState({});
      return;
    }

    try {
      setApplyingView(true);
      const gridState = selectedView.gridState;
      
      // Apply saved group by first if present (specific to this report)
      if (gridState.groupBy) {
        setGroupBy(gridState.groupBy);
      }
      
      // Try to apply full state first (newer saves)
      if (gridState.fullState) {
        api.setState(gridState.fullState);
        
        // Add a small delay to ensure filters are applied
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // If filters weren't applied by fullState, try applying them individually as fallback
        if (gridState.filterModel && Object.keys(gridState.filterModel).length > 0) {
          const currentFilters = api.getFilterModel() || {};
          const hasExpectedFilters = Object.keys(gridState.filterModel).every(key => 
            currentFilters[key] && JSON.stringify(currentFilters[key]) === JSON.stringify(gridState.filterModel[key])
          );
          
          if (!hasExpectedFilters) {
            await new Promise(resolve => setTimeout(resolve, 50));
            api.setFilterModel(gridState.filterModel);
            await new Promise(resolve => setTimeout(resolve, 50));
          }
        }
      } else {
        // Fallback to individual components (older saves or partial state)
        // Apply column state first
        if (gridState.columnState) {
          // Filter out the auto group column from saved state to prevent conflicts
          const filteredColumnState = gridState.columnState.filter(col => 
            col.colId !== 'ag-Grid-AutoColumn'
          );
          
          api.applyColumnState({
            state: filteredColumnState,
            applyOrder: true,
            applySize: true,
            applyVisible: true,
            applySort: false // Apply sorting separately
          });
        }

        // Apply column group state
        if (gridState.columnGroupState) {
          api.setColumnGroupState(gridState.columnGroupState);
        }

        // Apply sorting
        if (gridState.sortModel) {
          api.setSortModel(gridState.sortModel);
        }

        // Apply row grouping
        if (gridState.rowGroupCols && gridState.rowGroupCols.length > 0) {
          api.setRowGroupColumns(gridState.rowGroupCols);
        }

        // Apply pivot columns
        if (gridState.pivotCols && gridState.pivotCols.length > 0) {
          api.setPivotColumns(gridState.pivotCols);
        }

        // Apply value columns
        if (gridState.valueCols && gridState.valueCols.length > 0) {
          api.setValueColumns(gridState.valueCols);
        }

        // Apply pivot mode
        if (gridState.pivotMode !== undefined) {
          api.setPivotMode(gridState.pivotMode);
        }

        // Apply filters LAST with a delay to ensure they stick
        if (gridState.filterModel) {
          await new Promise(resolve => setTimeout(resolve, 100));
          api.setFilterModel(gridState.filterModel);
          await new Promise(resolve => setTimeout(resolve, 50));
        }
      }
      
      // Refresh the grid to ensure autoGroupColumnDef is updated
      api.refreshClientSideRowModel('group');
      
      // Auto-size all columns to fit content
      api.sizeColumnsToFit();
      
      // Also auto-size the auto group column specifically
      const autoGroupCol = api.getColumnDef('ag-Grid-AutoColumn');
      if (autoGroupCol) {
        api.autoSizeColumns(['ag-Grid-AutoColumn']);
      }
    } catch (error) {
      console.error('Error applying view state:', error);
      message.error('Failed to apply saved view');
      // Reset to default state on error
      api.setState({});
    } finally {
      setApplyingView(false);
    }
  };

  const handleViewChange = async (value) => {
    setSelectedViewId(value);
    try {
      await setDoc(doc(db, 'users', userData.id), {
        lastInventoryExceptionView: value || null,
      }, { merge: true });
      
      if (value) {
        await applyViewState(value);
      } else {
        // Clear view - reset grid state
        if (gridRef.current?.api) {
          const api = gridRef.current.api;
          api.setState({});
        }
      }
    } catch (error) {
      console.error('Error updating user view preference:', error);
      message.error('Failed to update view preference');
    }
  };

  // Toggle view visibility function
  const handleToggleViewVisibility = async (id) => {
    const view = savedViews.find(v => v.id === id);
    if (!view || view.createdBy !== userData?.id) {
      message.error('You can only modify your own views');
      return;
    }
    try {
      await setDoc(doc(db, 'inventoryExceptionViews', id), { isPublic: !view.isPublic }, { merge: true });
      message.success(`View ${!view.isPublic ? 'made public' : 'made private'}`);
    } catch (error) {
      console.error('Error toggling view visibility:', error);
      message.error('Failed to update view visibility');
    }
  };

  // Delete view function
  const handleDeleteView = async (id) => {
    const confirmed = window.confirm(`Are you sure you want to delete the view "${id?.replace(/[<>\"'&]/g, '')}"?`);
    if (!confirmed) return;
    await deleteDoc(doc(db, 'inventoryExceptionViews', id));
    setSavedViews(views => views.filter(v => v.id !== id));
    if (selectedViewId === id) {
      setSelectedViewId(null);
      if (gridRef.current && gridRef.current.api) {
        gridRef.current.api.resetColumnState();
        gridRef.current.api.resetColumnGroupState();
        gridRef.current.api.setFilterModel(null);
        gridRef.current.api.setRowGroupColumns([]);
      }
    }
    
    // If this was the user's last used view, clear the reference
    if (userData?.id && userData.lastInventoryExceptionView === id) {
      setDoc(doc(db, 'users', userData.id), { 
        lastInventoryExceptionView: null 
      }, { merge: true });
    }
  };

  // Main data fetch
  const fetchData = async () => {
    try {
      setLoading(true);
      // const res = await api.getInventoryExceptionsOnCall();
      const totalStart = new Date();
      const exceptionStart = new Date();
      
      // Validate the API call parameters
      const colorPromise = api.bigQueryRunQueryOnCall({
        options: {
          query: `
                      SELECT code, color
                      FROM \`hj-reporting.forecast.forecast_nodes\`
                    `
        }
      });
      
      const storage = getStorage();
      const inventoryExceptionReportRef = ref(storage, INVENTORY_EXCEPTION_REPORT_URL);
      const inventoryExceptionReportUrl = await getDownloadURL(inventoryExceptionReportRef); // Get signed URL
      
      // Validate the URL before making the request
      if (!inventoryExceptionReportUrl || typeof inventoryExceptionReportUrl !== 'string') {
        throw new Error('Invalid inventory exception report URL');
      }
      
      const inventoryExceptionReportResponse = await fetch(inventoryExceptionReportUrl);
      if (!inventoryExceptionReportResponse.ok) {
        throw new Error(`Failed to fetch inventory exception report: ${inventoryExceptionReportResponse.statusText}`);
      }
      
      const inventoryExceptionReport = await inventoryExceptionReportResponse.json();
      
      // Validate the response data
      if (!Array.isArray(inventoryExceptionReport)) {
        throw new Error('Invalid inventory exception report data format');
      }
      
      console.log("exceptionTime", (new Date() - exceptionStart) / 1000);
      const colorStart = new Date();
      const colorResults = await colorPromise;
      console.log("colorTime", (new Date() - colorStart) / 1000);
      console.log("inventoryExceptionReport", inventoryExceptionReport.slice(0, 10));
      
      setRowData(inventoryExceptionReport);
      
      // Validate color results before processing
      if (colorResults?.data && Array.isArray(colorResults.data)) {
        setNodeColors(colorResults.data.reduce((acc, curr) => {
          if (curr && curr.code && curr.color) {
            acc[curr.code] = curr.color;
          }
          return acc;
        }, {}));
      }
      
      console.log("totalTime", (new Date() - totalStart) / 1000);
    } catch (err) {
      console.error('Error fetching data:', err);
      message.error('Failed to load inventory data');
      setRowData([]);
      setNodeColors({});
    } finally {
      setLoading(false);
    }
  };
  const fetchLastUpdate = async () => {
    const unsub = onSnapshot(doc(db, "lists", "lastUpdates"), (doc) => {
      console.log("doc.data()", doc.data());
      setLastUpdate(doc.data().inventoryExceptionReport);
      unsub();
    });
  };

  useEffect(() => {
    fetchData();
    fetchLastUpdate();
  }, []);

  // Separate effect for fetching views based on user
  useEffect(() => {
    if (userData?.id) {
      const unsubscribe = fetchViews();
      return unsubscribe;
    }
  }, [userData?.id]);

  // Load last used view after views are fetched
  useEffect(() => {
    if (savedViews.length > 0 && userData?.id) {
      loadLastUsedView();
    }
  }, [savedViews, userData?.id]);

  // Filter data - always filter to 'Launching' and 'Active' life status items
  const filteredRowData = useMemo(() => {
    let filtered = rowData;
    // .filter(row =>
    //   row.life_status === 'Launching' || row.life_status === 'Active'
    // );
    if (groupBy !== 'product') {
      // When not grouping by product, remove product_type rows but keep forecast nodes
      filtered = filtered.filter(row => row.type !== 'product_type');
    }
    let tempColumnDefs = [
      {
        field: 'upc',
        headerName: 'UPC',
        width: 100,
        hide: true,
        cellRenderer: (params) => {
          return <Tooltip title="Double click to view item in Netsuite" placement="top">
            {params.value ? params.value : ''}
          </Tooltip>;
        },
        cellStyle: ({ value, data }) => {
          if (data?.type === 'main' && value > 0) {
            return {
              textDecoration: 'underline',
              color: '#1890ff', // blue
            };
          }
          return null;
        },
        onCellDoubleClicked: (params) => {
          if (params.data?.type === 'main' && params.data.item_id) {
            // Sanitize the item_id to prevent injection attacks
            const sanitizedId = String(params.data.item_id).replace(/[^0-9]/g, '');
            if (sanitizedId) {
              const url = `https://6810379.app.netsuite.com/app/common/item/item.nl?id=${sanitizedId}`;
              window.open(url, '_blank', 'noopener,noreferrer');
            }
          }
        }
      },
      {
        field: 'classification',
        headerName: 'Classification',
        width: 100,
      },
      {
        field: 'launch_date',
        headerName: 'Launch Date',
        valueGetter: (params) => params.data?.launch_date?.value ?? params.data?.launch_date ?? '',
        width: 100,
      },
      {
        field: 'end_date',
        headerName: 'End Date',
        valueGetter: (params) => params.data?.end_date?.value ?? params.data?.end_date ?? '',
        width: 100,
        cellStyle: ({ value, data }) => {
          const launchRaw = new Date(data?.launch_date?.value ?? data?.launch_date ?? '1900-01-01');
          const endRaw = new Date(data?.end_date?.value ?? data?.end_date ?? '2100-01-01');
          if (data?.type === 'main' && (launchRaw > endRaw)) {
            return {
              color: '#a8071a', // dark red
              fontWeight: 'bold',
            };
          }
          return null;
        }
      },
      {
        field: 'product',
        headerName: 'Product',
        width: 100,
        hide: true,
      },
      {
        field: 'division',
        headerName: 'Division',
        width: 100,
        hide: true,
      },
      {
        field: 'specification',
        headerName: 'Specification',
        width: 100,
        hide: true,
      },
      {
        field: 'color',
        headerName: 'Color',
        width: 100,
        hide: true,
      },
      {
        field: 'size',
        headerName: 'Size',
        width: 100,
        hide: true,
      },
      {
        field: 'life_status',
        headerName: 'Life Status',
        width: 100,
        hide: true,
      },
      {
        field: 'min_stock',
        headerName: 'Min Stock',
        width: 100,
        hide: true,
        cellRenderer: (params) => {
          return params.value ? parseInt(params.value).toLocaleString() : '';
        }
      },
      {
        field: 'max_stock',
        headerName: 'Max Stock',
        width: 100,
        hide: true,
        cellRenderer: (params) => {
          return params.value ? parseInt(params.value).toLocaleString() : '';
        }
      },
      {
        field: 'safety_stock',
        headerName: 'Safety Stock',
        width: 100,
        hide: true,
        cellRenderer: (params) => {
          return params.value ? parseInt(params.value).toLocaleString() : '';
        }
      },
      {
        field: 'on_hand',
        headerName: 'On Hand',
        valueGetter: (params) => params.data?.on_hand ?? null,
        width: 100,
        cellRenderer: (params) => {
          return params.value ? parseInt(params.value).toLocaleString() : '';
        }
      },
      {
        field: 'oh_value',
        headerName: 'On Hand Value',
        valueGetter: (params) => params.data?.oh_value ?? null,
        width: 100,
        cellRenderer: (params) => {
          return params.value ? parseInt(params.value).toLocaleString('en-US', {
            style: 'currency',
            currency: 'USD'
          }) : '';
        }
      },
      {
        field: 'avgcost',
        headerName: 'Avg Cost',
        valueGetter: (params) => params.data?.avgcost ?? null,
        width: 100,
        cellRenderer: (params) => {
          return params.value ? parseInt(params.value).toLocaleString('en-US', {
            style: 'currency',
            currency: 'USD'
          }) : '';
        }
      },
      {
        field: 'msrp',
        headerName: 'MSRP',
        valueGetter: (params) => params.data?.msrp ?? null,
        width: 100,
        cellRenderer: (params) => {
          return params.value ? parseInt(params.value).toLocaleString('en-US', {
            style: 'currency',
            currency: 'USD'
          }) : '';
        }
      },
      {
        field: 'revenue_potential',
        headerName: 'Rev Potential',
        valueGetter: (params) => params.data?.revenue_potential ?? null,
        width: 100,
        cellRenderer: (params) => {
          return params.value ? parseInt(params.value).toLocaleString('en-US', {
            style: 'currency',
            currency: 'USD'
          }) : '';
        }
      },
      {
        field: 'available',
        headerName: 'Available',
        valueGetter: (params) => params.data?.available ?? null,
        width: 100,
        cellStyle: ({ value, data }) => {
          if (data?.type === 'main') {
            if (value < 0) {
              return {
                color: '#a8071a', // dark red
                textDecoration: 'underline',
                fontWeight: 'bold',
              };
            }
            return {
              textDecoration: 'underline',
            };
          } else {
            return {
              color: '#000000',
              opacity: 0.0,
              fontSize: '10px'
            };
          }
        },
        cellRenderer: (params) => {
          if (params.data?.type === 'main') {
            return <Tooltip title="Double click to view in order allocation page" placement="top">
              <div style={{ cursor: 'pointer' }}>{params.value ? params.value.toLocaleString() : ''}</div>
            </Tooltip>;
          }
          return params.value ? params.value.toLocaleString() : '';
        },
        onCellDoubleClicked: (params) => {
          if (params.data?.type === 'main' && params.data.upc) {
            // Sanitize the UPC to prevent injection attacks
            const sanitizedUPC = String(params.data.upc).replace(/[^0-9A-Za-z]/g, '');
            if (sanitizedUPC) {
              const url = `/order-allocation?upc=${encodeURIComponent(sanitizedUPC)}`;
              window.open(url, '_blank', 'noopener,noreferrer');
            }
          }
        }
      },
      {
        field: 'reserve_qty',
        headerName: 'Reserve Qty',
        valueGetter: (params) => params.data?.reserve_qty ?? null,
        width: 100,
        hide: true,
        cellRenderer: (params) => {
          return params.value ? parseInt(params.value).toLocaleString() : '';
        }
      },
      // New columns
      {
        field: 'out_of_stock_days',
        headerName: 'Days OOS',
        width: 150,
        cellStyle: ({ value, data }) => {
          if (data?.type === 'main' && value > 0) {
            return {
              color: '#a8071a', // dark red
              fontWeight: 'bold',
            };
          }
          return null;
        },
        cellRenderer: (params) => {
          return params.value ? parseInt(params.value).toLocaleString() : '';
        }
      },
      {
        field: 'days_heavy',
        headerName: 'Days Heavy',
        valueGetter: (params) => params.data?.days_heavy ?? null,
        width: 100,
        cellRenderer: (params) => {
          return params.value ? parseInt(params.value).toLocaleString() : '';
        }
      },
      {
        field: 'days_light',
        headerName: 'Days Light',
        valueGetter: (params) => params.data?.days_light ?? null,
        width: 100,
        cellRenderer: (params) => {
          return params.value ? parseInt(params.value).toLocaleString() : '';
        }
      },
      {
        field: 'next_inbound_date',
        headerName: 'Next Inbound',
        valueGetter: (params) => params.data?.next_inbound_date ?? null,
        width: 140,
        cellRenderer: (params) => {
          return params.value ? dayjs(params.value).format('YYYY-MM-DD') : '';
        }
      },
      {
        field: 'total_inbound',
        headerName: 'Total Inbound',
        valueGetter: (params) => params.data?.total_inbound ?? null,
        width: 140,
        cellRenderer: (params) => {
          return <Tooltip title="Double click to view inbound details" placement="top">
            {params.value ? params.value.toLocaleString() : ''}
          </Tooltip>;
        },
        cellStyle: ({ value, data }) => {
          if (data?.type === 'main' && value > 0) {
            return {
              textDecoration: 'underline',
              color: '#1890ff', // blue
            };
          }
          return null;
        },
        onCellDoubleClicked: (params) => {
          if (params.data?.type === 'main') {
            setInboundModalVisible(true);
            setInboundModalData({
              upc: params.data.upc,
              product: params.data.product,
              color: params.data.color,
              size: params.data.size,
              inboundDetails: params.data.inbound_details
            });
          }
        }
      },
      {
        field: 'needsOrder',
        headerName: 'Order?',
        valueGetter: (params) => params.data?.needs_order ?? null,
        width: 120,
        cellRenderer: (params) => {
          if (params.data?.type === 'main') {
            return params.value ? 'Yes' : 'No';
          }
          return '';
        },
        cellStyle: ({ value, data }) => {
          if (data?.type === 'main' && value) {
            return {
              color: '#a8071a', // dark red
              fontWeight: 'bold',
            };
          }
          return null;
        }
      },
      ...nextDates.map(date => ({
        field: date,
        headerName: dayjs(date).format('M/D'),
        width: 100,
        cellStyle: ({ value, data }) => {
          const cellDay = new Date(date);
          const launchRaw = new Date(data?.launch_date?.value ?? data?.launch_date ?? '1900-01-01');
          const endRaw = new Date(data?.end_date?.value ?? data?.end_date ?? '2100-01-01');

          if (data?.type === 'main') {
            if (cellDay < launchRaw || cellDay > endRaw) {
              return {
                backgroundColor: '#000000',
                color: value > 0 ? '#ffffff' : '#000000',
                fontStyle: 'italic',
                fontWeight: 'bold',
              };
            }
            if (value < 0) {
              return {
                color: '#a8071a', // dark red
                fontStyle: 'italic',
                fontWeight: 'bold',
              };
            }
            if (value > 0) {
              if (value < data?.min_stock) {
                return {
                  color: '#fa8c16',
                  fontStyle: 'italic',
                  fontWeight: 'bold',
                };
              }
              if (value > data?.max_stock) {
                return {
                  color: '#722ed1',
                  fontStyle: 'italic',
                  fontWeight: 'bold',
                };
              }
            }
          }
          if (data?.type === 'in') {
            if (value > 0) {
              return {
                color: '#1890ff', // blue
                fontWeight: 'bold',
                fontSize: '10px'
              };
            }
            return {
              color: '#000000',
              opacity: 0.0,
              fontSize: '10px'
            };
          }
          if (data?.type === 'out') {
            if (value > 0) {
              return {
                color: '#ff4d4f',
                fontStyle: 'italic',
                fontSize: '10px'
              };
            }
            return {
              color: '#000000',
              opacity: 0.0,
              fontSize: '10px'
            };
          }
          if (data?.type === 'forecast_node') {
            if (value > 0) {
              return {
                fontSize: '10px'
              };
            }
            return {
              opacity: 0.0,
              fontSize: '10px'
            };
          }
          return null;
        }
      })),
    ];
    setColumnDefs(tempColumnDefs);
    return filtered;
  }, [rowData, groupBy]);

  // Summary stats
  const summaryStats = useMemo(() => {
    const mainRows = filteredRowData.filter(row => row.type === 'main') || [];
    return {
      total: mainRows.length,
      active: mainRows.filter(row => row.life_status === 'Active').length,
      launching: mainRows.filter(row => row.life_status === 'Launching').length,
      outOfStock: mainRows.filter(row => (row.on_hand || 0) <= 0).length,
    };
  }, [filteredRowData]);

  // Export CSV
  const handleExport = () => {
    if (gridRef.current && gridRef.current.api) {
      gridRef.current.api.exportDataAsExcel({
        fileName: 'inventory_exceptions.xlsx'
      });
    }
  };

  // Refresh function
  const handleRefresh = async () => {
    setLoading(true);
    message.success('Inventory Exception Report is being refreshed. This may take a few moments.', 10);
    const refreshed = await api.getInventoryExceptionsOnCall();
    await fetchData();
    await fetchLastUpdate();
    setLoading(false);
  };


  // Group aggregation function - Optimized version
  const groupAggFunction = useCallback((params) => {
    const result = {};
    const children = params.children;
    const childrenLength = children.length;
    
    // Pre-allocate arrays for better performance
    for (let i = 0; i < nextDates.length; i++) {
      const date = nextDates[i];
      let sum = 0;
      for (let j = 0; j < childrenLength; j++) {
        sum += children[j].data[date] || 0;
      }
      result[date] = sum;
    }
    
    // Aggregate qty
    let qtySum = 0;
    for (let i = 0; i < childrenLength; i++) {
      qtySum += children[i].data.qty || 0;
    }
    result.qty = qtySum;
    
    return result;
  }, [nextDates]);


  const onClearFilters = () => {
    if (gridRef.current && gridRef.current.api) {
      gridRef.current.api.setFilterModel(null);
    }
  };
    // BUILD getDataPath BASED ON groupBy SELECTION - Optimized version
  const getDataPath = useCallback((data) => {
    // Early return for product grouping to avoid unnecessary processing
    if (groupBy === 'product') {
      if (data.type === 'forecast_node') {
        return [...(data.path || []), data.code || 'forecast'];
      }
      return data.path;
    }
    
    // For non-product groupings, determine the base key once
    let baseKey;
    if (data.type === 'forecast_node') {
      // For forecast nodes, they should always be at the deepest level (children)
      switch (groupBy) {
        case 'launch_date':
          baseKey = (data.launch_date?.value || data.launch_date || '')?.toString().split('T')[0] || 'Unknown';
          break;
        case 'division':
          baseKey = data.division || 'Unknown';
          break;
        case 'classification':
          baseKey = data.classification || 'Unknown';
          break;
        case 'color':
          baseKey = data.color || 'Unknown';
          break;
        default:
          baseKey = data.product || 'Unknown';
      }
      
      // Create path ensuring forecast nodes are children
      if (Array.isArray(data.path)) {
        const remainingPath = data.path.slice(1);
        return [baseKey, ...remainingPath, data.code || 'forecast'];
      }
      return [baseKey, data.code || 'forecast'];
    }
    
    // For non-forecast nodes, determine the base key
    switch (groupBy) {
      case 'launch_date':
        baseKey = (data.launch_date?.value || data.launch_date || '')?.toString().split('T')[0] || 'Unknown';
        break;
      case 'division':
        baseKey = data.division || 'Unknown';
        break;
      case 'classification':
        baseKey = data.classification || 'Unknown';
        break;
      case 'color':
        baseKey = data.color || 'Unknown';
        break;
      default:
        baseKey = data.product || 'Unknown';
    }
    
    // Create path for non-forecast nodes
    if (Array.isArray(data.path)) {
      const remainingPath = data.path.slice(1);
      return [baseKey, ...remainingPath];
    }
    
    return [baseKey];
  }, [groupBy]);

  // Refresh grouping when groupBy changes - Optimized version
  useEffect(() => {
    if (gridRef.current && gridRef.current.api && debouncedGroupBy !== groupBy) {
      setGroupingLoading(true);
      
      // Use requestAnimationFrame for better performance
      requestAnimationFrame(() => {
        if (gridRef.current && gridRef.current.api) {
          const api = gridRef.current.api;
          
          // Batch operations for better performance
          api.suppressAnimationFrame = true;
          
          // Refresh the grid to update column definitions and grouping
          api.refreshClientSideRowModel('group');
          
          // Only expand the top level (level 0) - more efficient approach
          api.expandAll();
          
          // Efficiently collapse all levels except the top level
          const nodesToCollapse = [];
          api.forEachNode(node => {
            if (node.level > 0) {
              nodesToCollapse.push(node);
            }
          });
          
          // Batch collapse operations
          nodesToCollapse.forEach(node => node.setExpanded(false));
          
          // Clear any existing filters on the auto group column
          const filterModel = api.getFilterModel();
          if (filterModel && filterModel['ag-Grid-AutoColumn']) {
            delete filterModel['ag-Grid-AutoColumn'];
            api.setFilterModel(filterModel);
          }
          
          // Re-enable animations
          api.suppressAnimationFrame = false;
          
          setGroupingLoading(false);
        }
      });
    }
  }, [debouncedGroupBy, groupBy]);

  return (
    <div className="inventory-exception-full-page" style={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      <Card
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
            <span>Inventory Exception Report</span>
            <Text type="secondary">
              ({summaryStats.active} active, {summaryStats.launching} launching, {summaryStats.outOfStock} out of stock, {summaryStats.total} total)
            </Text>
            <Text type="secondary">
              Last updated: {lastUpdate ? lastUpdate.toDate().toLocaleString() : 'Never'}
            </Text>
          </div>
        }
        loading={loading}
        extra={
          <div style={{ display: 'flex', gap: 8, alignItems: 'center' }}>
            <Tooltip title="Refresh the inventory exception report data">
              <Button type="primary" icon={<ReloadOutlined />} onClick={handleRefresh} />
            </Tooltip>
            <Tooltip title="Group rows by">
              <Select
                style={{ minWidth: 140 }}
                value={groupBy}
                onChange={val => setGroupBy(val)}
                loading={groupingLoading}
                options={[
                  { label: 'Product', value: 'product' },
                  { label: 'Launch Date', value: 'launch_date' },
                  { label: 'Division', value: 'division' },
                  { label: 'Classification', value: 'classification' },
                  { label: 'Color', value: 'color' },
                ]}
              />
            </Tooltip>
            <Button type="default" icon={<InfoCircleOutlined />} onClick={() => setLegendVisible(true)} />
            <Button type="primary" icon={<DownloadOutlined />} onClick={handleExport} disabled={loading} />
            <Tooltip title="Clear all filters">
              <Button onClick={onClearFilters} icon={<CloseCircleOutlined />} />
            </Tooltip>
            <Tooltip title="Select a previously saved grid layout, including filters, column visibility, and sorting.">
              <Select
                style={{ minWidth: 200 }}
                placeholder="Select Saved View"
                value={selectedViewId}
                onChange={handleViewChange}
                loading={applyingView}
                options={savedViews.map(v => ({ label: v.name, value: v.id }))}
                dropdownRender={menu => (
                  <div style={{ maxHeight: 300, overflowY: 'auto' }}>
                    {savedViews.length === 0 && <div style={{ padding: 8, color: '#888' }}>No saved views</div>}
                    {savedViews.map(view => (
                      <div key={view.id} style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', padding: '4px 12px', cursor: 'pointer' }}
                        onClick={() => handleSelectView(view.id)}
                      >
                        <span>{view.name}</span>
                        <div style={{ display: 'flex', gap: 4 }}>
                          {view.createdBy === userData?.id && (
                            <Tooltip title={view.isPublic ? 'Public View' : 'Private View'}>
                              <ShareOutlined
                                className={view.isPublic ? 'shared' : 'not-shared'}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleToggleViewVisibility(view.id);
                                }}
                                style={{ color: view.isPublic ? '#1890ff' : '#666', cursor: 'pointer' }}
                              />
                            </Tooltip>
                          )}
                          {view.createdBy === userData?.id && (
                            <Popconfirm title="Are you sure you want to delete this view?" onConfirm={(e) => {
                              e.stopPropagation();
                              handleDeleteView(view.id);
                            }}>
                              <DeleteOutlined
                                style={{ color: '#ff4d4f', cursor: 'pointer' }}
                                onClick={(e) => e.stopPropagation()}
                              />
                            </Popconfirm>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
                allowClear
              />
            </Tooltip>
            <Tooltip title="Save current table configuration (filters, columns, sorting) as a reusable view that you can quickly load later">
              <Button
                icon={<SaveOutlined />}
                type="primary"
                onClick={handleSaveView}
                disabled={applyingView}
              >
                Save View
              </Button>
            </Tooltip>

          </div>
        }
        style={{ margin: '16px', flexShrink: 0 }}
      >

      </Card>

      {/* Grid - fills remaining space */}
      <div className="grid-wrapper" style={{ flex: 1, margin: '0 16px 16px 16px', minHeight: 0 }}>
        <div style={{ height: 'calc(100vh - 200px)' }}>
          <AgGridReact
            ref={gridRef}
            rowData={filteredRowData}
            columnDefs={columnDefs}
            loading={loading}
            pagination={true}
            paginationPageSize={userData.pageSize || 50}
            paginationPageSizeSelector={[25, 50, 100, 200]}
            autoSizeStrategy={{ type: 'fitCellContents' }}
            sideBar={{
              toolPanels: [
                {
                  id: 'columns',
                  labelDefault: 'Columns',
                  labelKey: 'columns',
                  iconKey: 'columns',
                  toolPanel: 'agColumnsToolPanel',
                },
                {
                  id: 'filters',
                  labelDefault: 'Filters',
                  labelKey: 'filters',
                  iconKey: 'filter',
                  toolPanel: 'agFiltersToolPanel',
                }
              ],
            }}
            autoGroupColumnDef={{
              headerName: groupBy === 'product' ? 'Product' : 
                         groupBy === 'launch_date' ? 'Launch Date' :
                         groupBy === 'division' ? 'Division' :
                         groupBy === 'classification' ? 'Classification' :
                         groupBy === 'color' ? 'Color' : 'Item',
              cellRendererParams: { suppressCount: true },
              pinned: 'left',
              filter: 'agSetColumnFilter',
              valueGetter: (params) => {
                // For grouped rows, return the group key
                if (params.node.group) {
                  return params.node.key;
                }
                // For leaf nodes, return the appropriate field based on grouping
                switch (groupBy) {
                  case 'launch_date':
                    return (params.data.launch_date?.value || params.data.launch_date || '')?.toString().split('T')[0] || 'Unknown';
                  case 'division':
                    return params.data.division || 'Unknown';
                  case 'classification':
                    return params.data.classification || 'Unknown';
                  case 'color':
                    return params.data.color || 'Unknown';
                  case 'product':
                  default:
                    return params.data.product || 'Unknown';
                }
              },
              cellStyle: (params) => {
                // Check if this is a grouped row (has children) and is a product group
                if (params.node.group && params.node.data?.type === 'product_type') {
                  return { fontWeight: 'bold' };
                }
                if (params.node?.data?.type === 'main') {
                  const status = params.node.data.life_status;
                  return { color: lifeStatusColors[status] };
                }
                if (params.node?.data?.type === 'forecast_node') {
                  const node = params.value;
                  return { color: nodeColors[node], fontWeight: 'bold' };
                }
                return null;
              }
            }}
            rowSelection={{
              type: 'multiple'
            }}
            rowClassRules={{
              'ag-row-even': params => params.rowIndex % 2 === 0,
              'ag-row-odd': params => params.rowIndex % 2 !== 0,
              'inventory-exception-main': params => params.data?.type === 'main',
              'inventory-exception-in': params => params.data?.type === 'in',
              'inventory-exception-out': params => params.data?.type === 'out',
              'inventory-exception-node': params => params.data?.type === 'forecast_node',
              'inventory-exception-product-type': params => params.node?.level === 0,
            }}
            suppressRowClickSelection={true}
            enableRangeSelection={true}
            suppressCopySingleCellRanges={true}
            treeData={true}
            getDataPath={getDataPath}
            groupAggFunction={groupAggFunction}
            groupDefaultExpanded={1}
            defaultColDef={{
              resizable: true,
              sortable: true,
              filter: true
            }}
            // Performance optimizations
            suppressAnimationFrame={true}
            suppressColumnVirtualisation={false}
            suppressRowVirtualisation={false}
            rowBuffer={20}
            maxBlocksInCache={10}
            cacheBlockSize={100}
            theme={themeBalham}
          />
        </div>
      </div>

      {/* Modals */}
      <Modal
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={null}
        title={detailModalData ? `${detailModalData.product?.replace(/[<>\"'&]/g, '') || 'Unknown'} ${detailModalData.color?.replace(/[<>\"'&]/g, '') || 'Unknown'} ${detailModalData.size?.replace(/[<>\"'&]/g, '') || 'Unknown'}` : 'Product Details'}
        width={600}
      >
        {detailModalData && (
          <Descriptions bordered column={1} size="small">
            <Descriptions.Item label="UPC">{detailModalData.path?.[0] || detailModalData.upc} <CopyToClipboardIcon valueToCopy={detailModalData.path?.[0] || detailModalData.upc} /></Descriptions.Item>
            <Descriptions.Item label="Product">{detailModalData.product_type}</Descriptions.Item>
            <Descriptions.Item label="Life Status">{detailModalData.life_status}</Descriptions.Item>
            <Descriptions.Item label="Launch Date">{detailModalData.launchDate || detailModalData.launchdate || '-'}</Descriptions.Item>
            <Descriptions.Item label="End Date">{detailModalData.enddate || '-'}</Descriptions.Item>
            <Descriptions.Item label="On Hand">{detailModalData.on_hand != null ? Number(detailModalData.on_hand).toLocaleString() : ''}</Descriptions.Item>
            <Descriptions.Item label="On Order">{detailModalData.qtyOnOrder != null ? Number(detailModalData.qtyOnOrder).toLocaleString() : ''}</Descriptions.Item>
          </Descriptions>
        )}
      </Modal>

      <Modal
        open={legendVisible}
        onCancel={() => setLegendVisible(false)}
        footer={null}
        title="Color Legend"
      >
        <ul style={{ lineHeight: '24px', paddingLeft: 16 }}>
          <li><span style={{ color: lifeStatusColors['Active'] }}>●</span> Active life status</li>
          <li><span style={{ color: lifeStatusColors['Launching'] }}>●</span> Launching life status</li>
          <li><span style={{ color: lifeStatusColors['Phasing Out'] }}>●</span> Phasing Out life status</li>
          <li><span style={{ color: lifeStatusColors['Obsolete'] }}>●</span> Obsolete life status</li>
          <li><span className="oos-cell">0</span> Out-of-stock cell</li>
          <li><span className="launch-cell">L</span> Launch date cell</li>
          <li><span className="end-cell">E</span> End date cell</li>
          <li><span style={{ color: '#006400', fontStyle: 'italic' }}>In</span> Inbound row</li>
          <li><span style={{ color: '#a8071a', fontStyle: 'italic' }}>Out</span> Outgoing row</li>
        </ul>
      </Modal>

      <Modal
        title={modalData ? `${modalData?.type?.replace(/[<>\"'&]/g, '') || 'Unknown'} Details - ${modalData?.date?.replace(/[<>\"'&]/g, '') || 'Unknown'}` : 'Details'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={800}
      >
        {modalData && (
          <Table
            dataSource={modalData.details}
            columns={[
              { title: 'Type', dataIndex: 'type', key: 'type' },
              { title: 'Quantity', dataIndex: 'qty', key: 'qty', render: (value) => value ? value.toLocaleString() : '' },
              {
                title: 'Details',
                key: 'details',
                render: (record) => (
                  <div>
                    {modalData?.type === 'Incoming' ? (
                      <>
                        {record.poNumber && <div>PO: {record.poNumber.replace(/[<>\"'&]/g, '')}</div>}
                        {record.containerNum && <div>Container: {record.containerNum.replace(/[<>\"'&]/g, '')}</div>}
                        {record.loc && <div>Location: {record.loc.replace(/[<>\"'&]/g, '')}</div>}
                      </>
                    ) : (
                      <>
                        {record.forecast_node && <div>Forecast Node: {record.forecast_node.replace(/[<>\"'&]/g, '')}</div>}
                        {record.customer && <div>Customer: {record.customer.replace(/[<>\"'&]/g, '')}</div>}
                        {record.document_number && <div>Order: {record.document_number.replace(/[<>\"'&]/g, '')}</div>}
                      </>
                    )}
                  </div>
                )
              }
            ]}
            pagination={false}
            size="small"
            rowKey={(record, index) => index}
          />
        )}
      </Modal>

      <Modal
        title={inboundModalData ? `Inbound Details - ${inboundModalData.product?.replace(/[<>\"'&]/g, '') || 'Unknown'} ${inboundModalData.color?.replace(/[<>\"'&]/g, '') || 'Unknown'} ${inboundModalData.size?.replace(/[<>\"'&]/g, '') || 'Unknown'} ${inboundModalData.upc?.replace(/[<>\"'&]/g, '') || 'Unknown'}` : 'Inbound Details'}
        open={inboundModalVisible}
        onCancel={() => setInboundModalVisible(false)}
        footer={null}
        width={800}
      >
        {inboundModalData && (
          <div>
            <Table
              dataSource={inboundModalData.inboundDetails}
              columns={[
                {
                  title: 'Doc #',
                  dataIndex: 'tranId',
                  key: 'tranId',
                  render: (data, params) => {
                    if (params.tranId) {
                      if (params.type === 'Inbound') {
                        return <a href={`https://6810379.app.netsuite.com/app/accounting/transactions/shipping/inboundshipment/inboundshipment.nl?id=${params.id}`} target="_blank" rel="noopener noreferrer">{params.tranId}</a>;
                      }
                      if (params.type === 'Purchase Order') {
                        return <a href={`https://6810379.app.netsuite.com/app/accounting/transactions/purchord.nl?id=${params.id}&whence=`} target="_blank" rel="noopener noreferrer">{params.tranId}</a>;
                      }
                      return params.tranId;
                    }
                    return '-';
                  }
                },
                {
                  title: 'Container',
                  dataIndex: 'container',
                  key: 'container'
                },
                {
                  title: 'Qty',
                  dataIndex: 'qty',
                  key: 'qty',
                  render: (value) => value ? value.toLocaleString() : '0'
                },
                {
                  title: 'Expected Arrival',
                  dataIndex: 'expected_arrival',
                  key: 'expected_arrival',
                  render: (data) => {
                    if (data.value) {
                      return dayjs(data.value).format('YYYY-MM-DD');
                    }
                    return '-';
                  }
                }
              ]}
              pagination={false}
              size="small"
              rowKey={(record, index) => index}
            />
          </div>
        )}
      </Modal>
    </div>
  );
};

export default InventoryExceptionReport;
