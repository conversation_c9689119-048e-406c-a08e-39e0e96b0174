const { BigQuery } = require("@google-cloud/bigquery");
const fs = require("fs");
const os = require("os");
const path = require("path");
const { queryNetSuite, netsuiteSuiteQlQuery } = require("./netsuite");
const axios = require("axios");
const { queryDocs, getDocData, modifyDoc } = require("./firestore");
const { createTask, storageGetCsvFiles, } = require("./google");
const { TASK_URLS } = require("./constants");
const projectId = "hj-reporting";
const bigquery = new BigQuery({ projectId });
const { parse } = require("csv-parse/sync");
const { Storage } = require("@google-cloud/storage");
const storage = new Storage();

// Utility function to detect if running on localhost and modify dataset names for testing
const getTestingDataset = (datasetId) => {
  // Check if we're running on localhost by looking at environment variables or request headers
  // For Firebase Functions, we can check if we're in development mode
  // const isLocalhost = process.env.FUNCTIONS_EMULATOR === "true" || 
  //                    process.env.NODE_ENV === "development" ||
  //                    process.env.LOCALHOST === "true";
  
  // if (isLocalhost) {
  //   return `testing_${datasetId}`;
  // }
  return datasetId;
};

const bigQueryGetDatasetsAndTables = async () => {
  const datasets = await bigquery.getDatasets();
  const results = {};
  for (const dataset of datasets[0]) {
    results[dataset.id] = (await dataset.getTables())[0].map(x => x.id);
  }
  return results;
};


const bigQueryCreateDataset = async (datasetId) => {
  // Apply testing dataset logic
  const actualDatasetId = getTestingDataset(datasetId);
  
  // Reference the dataset
  const dataset = bigquery.dataset(actualDatasetId);

  try {
    // Check if the dataset exists
    const [exists] = await dataset.exists();

    if (!exists) {
      console.log(`Dataset ${actualDatasetId} not found. Creating dataset...`);

      // Create the dataset
      await bigquery.createDataset(actualDatasetId);
      console.log(`Dataset ${actualDatasetId} created successfully.`);
    } else {
      console.log(`Dataset ${actualDatasetId} already exists.`);
    }
  } catch (error) {
    console.error("Error checking or creating dataset:", error);
  }
  return dataset;
};
const bigQueryCreateTable = async (datasetId, tableId, rows = [], schema = null) => {
  // Apply testing dataset logic
  const actualDatasetId = getTestingDataset(datasetId);
  
  const dataset = bigquery.dataset(actualDatasetId);
  const table = dataset.table(tableId);
  const [exists] = await table.exists();
  if (!exists) {
    if (rows && rows.length) {
      if (!schema) {
        schema = buildSchemaFromRows(rows);
      }
      await table.create({ schema });
    } else {
      await table.create();
    }
    console.log(`Table ${tableId} created successfully.`);
  }
  //TODO handle schema changes
  return table;
};
const bigQueryCreateSnapshotTable = async (datasetId, tableId) => {
  const utahDate = new Date().toLocaleString("en-US", { 
    timeZone: "America/Denver",
    year: "numeric",
    month: "2-digit", 
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit",
    hour12: true
  });
  
  // Format: MM/DD/YYYY, HH:MM:SS AM/PM -> YYYY-MM-DD_HH_MM_SS_AM
  const parts = utahDate.split(", ");
  const datePart = parts[0].split("/");
  const timePart = parts[1].split(" ");
  const timeComponents = timePart[0].split(":");
  
  const month = datePart[0];
  const day = datePart[1];
  const year = datePart[2];
  const hour = timeComponents[0];
  const minute = timeComponents[1];
  const second = timeComponents[2];
  const ampm = timePart[1];
  
  const cleanTime = `${year}-${month}-${day}_${hour}_${minute}_${second}_${ampm}`;
  const snapshotTableId = `${datasetId}_${tableId}_${cleanTime}`;
  console.log("snapshotTableId", snapshotTableId);
  const snapshotDatasetId = "snapshots";
  await bigQueryCreateDataset(snapshotDatasetId);
  const query = `
    CREATE SNAPSHOT TABLE \`${projectId}.${snapshotDatasetId}.${snapshotTableId}\`
    CLONE \`${projectId}.${datasetId}.${tableId}\`
    OPTIONS (
      expiration_timestamp = TIMESTAMP_ADD(CURRENT_TIMESTAMP(), INTERVAL 30 DAY)
    )
  `;

  const options = {
    query: query,
    location: "US", // Update to your dataset's location
  };

  try {
    bigquery.createQueryJob(options);
    console.log(`Snapshot table ${snapshotTableId} job created successfully.`);
  } catch (error) {
    console.error("Error creating snapshot table:", error);
  }
};
const bigQueryReplaceTable = async (datasetId, tableId, newRows) => {
  // Apply testing dataset logic
  const actualDatasetId = getTestingDataset(datasetId);
  
  const dataset = bigquery.dataset(actualDatasetId);
  let table = dataset.table(tableId);
  const [exists] = await table.exists();
  if (!exists) {
    await bigQueryCreateDataset(actualDatasetId);
    //check if table exists and create if it doesn't
    table = await bigQueryCreateTable(actualDatasetId, tableId, newRows);
  }

  // Retrieve the existing schema
  let existingSchema;
  try {
    const [metadata] = await table.getMetadata();
    existingSchema = metadata.schema;
  } catch (error) {
    console.error(`Failed to retrieve schema from table ${tableId}:`, error);
    throw error;
  }

  // Clean the data by removing the "links" field from each row and sanitizing field names
  const cleanedRows = newRows.map(row => {
    const cleanRow = {};
    for (const key in row) {
      if (key !== "links") {
        const sanitizedKey = sanitizeFieldName(key);
        cleanRow[sanitizedKey] = row[key];
      }
    }
    return cleanRow;
  });

  // Remove any columns from cleanedRows that don't exist in the original schema
  let finalRows;
  try {
    finalRows = formatRows({ schema: existingSchema, rows: cleanedRows });
  } catch (error) {
    console.error("Error formatting rows", error.message, error.stack);
    throw error;
  }
  // const finalRows = cleanedRows.map(row => {
  //   const cleanRow = {};
  //   for (const key in row) {
  //     if (schemaFieldNames.includes(key)) {
  //       cleanRow[key] = row[key];
  //     }
  //   }
  //   return cleanRow;
  // });

  const metadata = {
    sourceFormat: "NEWLINE_DELIMITED_JSON",
    writeDisposition: "WRITE_TRUNCATE",
    schema: {
      fields: existingSchema.fields
    }
  };
  await bigQueryCreateSnapshotTable(datasetId, tableId);

  const tmpDir = os.tmpdir();
  const tmpFilePath = path.join(tmpDir, `bigquery-data-${Date.now()}.json`);
  try {
    // Stream the array of objects to NDJSON to avoid building a huge string in memory
    const writeStream = fs.createWriteStream(tmpFilePath, { encoding: "utf8" });
    try {
      for (const row of finalRows) {
        writeStream.write(JSON.stringify(row) + "\n");
      }
    } finally {
      // Ensure the stream is closed before proceeding
      await new Promise((resolve, reject) => {
        writeStream.end();
        writeStream.on("finish", resolve);
        writeStream.on("error", reject);
      });
    }
    console.log("Starting load job to replace table data...");
    const [job] = await table.load(tmpFilePath, metadata);
    console.log(`Load job ${job.id} started. Waiting for completion...`);
    // await job.promise();
    console.log("Load job completed successfully.");
  } catch (error) {
    console.error("Error during load job:", error);
    throw error;
  } finally {
    fs.unlinkSync(tmpFilePath);
  }
};
const bigQueryLoadToTable = async ({ datasetId, tableId, rows, replace = true }) => {
  // Apply testing dataset logic
  const actualDatasetId = getTestingDataset(datasetId);
  
  const dataset = bigquery.dataset(actualDatasetId);
  let table = dataset.table(tableId);
  const [exists] = await table.exists();
  if (!exists) {
    await bigQueryCreateDataset(actualDatasetId);
    //check if table exists and create if it doesn't
    table = await bigQueryCreateTable(actualDatasetId, tableId, rows);
  }

  // Retrieve the existing schema
  let existingSchema;
  try {
    const [metadata] = await table.getMetadata();
    existingSchema = metadata.schema;
  } catch (error) {
    console.error(`Failed to retrieve schema from table ${tableId}:`, error);
    throw error;
  }

  // Clean the data by removing the "links" field from each row and sanitizing field names
  const cleanedRows = rows.map(row => {
    const cleanRow = {};
    for (const key in row) {
      if (key !== "links") {
        const sanitizedKey = sanitizeFieldName(key);
        cleanRow[sanitizedKey] = row[key];
      }
    }
    return cleanRow;
  });

  // Remove any columns from cleanedRows that don't exist in the original schema
  let finalRows;
  try {
    finalRows = formatRows({ schema: existingSchema, rows: cleanedRows });
  } catch (error) {
    console.error("Error formatting rows", error.message, error.stack);
    throw error;
  }
  // const finalRows = cleanedRows.map(row => {
  //   const cleanRow = {};
  //   for (const key in row) {
  //     if (schemaFieldNames.includes(key)) {
  //       cleanRow[key] = row[key];
  //     }
  //   }
  //   return cleanRow;
  // });


  const metadata = {
    sourceFormat: "NEWLINE_DELIMITED_JSON",
    writeDisposition: "WRITE_TRUNCATE",
    schema: {
      fields: existingSchema.fields
    }
  };
  if (!replace) {
    metadata.writeDisposition = "WRITE_APPEND";
  }
  await bigQueryCreateSnapshotTable(datasetId, tableId);

  const tmpDir = os.tmpdir();
  const tmpFilePath = path.join(tmpDir, `bigquery-data-${Date.now()}.json`);
  try {
    // Stream the array of objects to NDJSON to avoid building a huge string in memory
    const writeStream = fs.createWriteStream(tmpFilePath, { encoding: "utf8" });
    try {
      for (const row of finalRows) {
        writeStream.write(JSON.stringify(row) + "\n");
      }
    } finally {
      await new Promise((resolve, reject) => {
        writeStream.end();
        writeStream.on("finish", resolve);
        writeStream.on("error", reject);
      });
    }
    console.log("Starting load job to replace table data...");
    await table.load(tmpFilePath, metadata);
    console.log("Load job completed successfully.");
  } catch (error) {
    console.error("Error during load job:", error);
    return false;
  } finally {
    fs.unlinkSync(tmpFilePath);
  }
  return true;
};
const bigQueryRunQueries = async (ids = []) => {
  let savedQueries = [];
  if (!ids || !ids.length) {
    savedQueries = await queryDocs("savedQueries", [{ key: "status", operator: "!=", value: "running" }]);
  } else {
    for (const id of ids) {
      const savedQuery = await getDocData("savedQueries", id);
      //TODO: check if savedQuery is valid
      savedQueries.push(savedQuery);
    }
  }
  for (const savedQuery of savedQueries) {
    //create a task to run the query and update the table
    const job = {
      savedQuery,
      type: "replace"
    };
    if (savedQuery.status !== "running") {
      await runUpdateBigQueryTask(job);
    }
  }
  return savedQueries;
};
const updateBigQueryReports = async () => {
  // eslint-disable-next-line max-len
  const searchQuery = `select id, custrecord_searh_id, custrecord_bq_project_id, custrecord_bq_dataset_id, custrecord_bq_table_id, custrecord_bq_sync_method from custom.customrecord1418 where custrecord_export_to_bq='T'`;
  const searches = await queryNetSuite(searchQuery, false);
  if (!searches || !searches.length) return false;
  if (typeof searches === "string") {
    console.error("Error:", searches);
    return false;
  }
  for (const s of searches) {
    try {
      //check if dataset exists and create if it doesn't
      await bigQueryCreateDataset(s.custrecord_bq_dataset_id);
      //check if table exists and create if it doesn't
      await bigQueryCreateTable(s.custrecord_bq_dataset_id, s.custrecord_bq_table_id);
      let rows = [];
      if (s.custrecord_searh_id) {
        // eslint-disable-next-line max-len
        const searchUrl = "https://6810379.extforms.netsuite.com/app/site/hosting/scriptlet.nl?script=1873&deploy=1&compid=6810379&ns-at=AAEJ7tMQ62yb0CjU7hRc7bEjjwQw9nJDo95m1agKif4C71WED_8&key=Hydr04L1fe!&source=api&searchId=" + s.custrecord_searh_id;
        const resp = await axios.get(searchUrl);
        rows = resp.data;
        if (rows.includes("error") || rows.length === 0) {
          console.error("No data for", JSON.stringify(s));
          continue;
        }
        rows.pop();
        if (!rows) {
          console.error("No data for", JSON.stringify(s));
          continue;
        }
        for (const row of rows) {
          for (const key in row) {
            if (!row[key]) {
              row[key] = null;
              continue;
            }
          }
        }
      } else {
        //pull suiteql
        console.error("No search id for", s.custrecord_bq_table_id);
        continue;
      }
      //add new columns if needed
      switch (s.custrecord_bq_sync_method) {
        case "1": //overwrite table
          await bigQueryReplaceTable(s.custrecord_bq_dataset_id, s.custrecord_bq_table_id, rows);
          break;
        default:
          console.error("Invalid sync method", s.custrecord_bq_sync_method);
          continue;
      }
    } catch (error) {
      console.error("Error", error);
    }
  }
};
const updateBigQueryTableFromSuiteQl = async ({ savedQuery, offset, type }) => {
  const pageSize = 1000;
  const maxPages = 10;
  let hasMore = true;
  let currentOffset = offset || 0;

  // Get the current date parameters from the saved query
  const dateParams = {
    startTime: savedQuery.startTime || new Date().toISOString(),
    endTime: savedQuery.endTime || new Date().toISOString()
  };

  // Process up to 10 pages of data
  const allResults = [];
  for (let i = 0; i < maxPages; i++) {
    const resp = await netsuiteSuiteQlQuery({
      q: savedQuery.query,
      limit: pageSize,
      offset: currentOffset,
      connector: savedQuery.connector,
      dateParams
    });
    allResults.push(...resp.results);
    currentOffset = resp.offset;
    hasMore = resp.hasMore;
    if (!hasMore) {
      break;
    }
  }
  console.log("All results", allResults.length, currentOffset, hasMore, savedQuery.query);
  const results = allResults;

  if (results.length > 0) {
    // Update BigQuery table with the results
    if (type === "replace") {
      await bigQueryReplaceTable(
        savedQuery.datasetId,
        savedQuery.tableId,
        results
      );
    } else if (type === "append") {
      await bigQueryAppendRows(
        savedQuery.datasetId,
        savedQuery.tableId,
        results
      );
    } else {
      throw new Error("Invalid type" + type);
    }

    // Calculate next date interval if interval is defined
    let nextStartTime = dateParams.startTime;
    let nextEndTime = dateParams.endTime;
    if (savedQuery.dateInterval) {
      const interval = savedQuery.dateInterval;
      const startDate = new Date(dateParams.startTime);
      const endDate = new Date(dateParams.endTime);

      // Advance the dates by the interval
      startDate.setHours(startDate.getHours() + interval);
      endDate.setHours(endDate.getHours() + interval);

      nextStartTime = startDate.toISOString();
      nextEndTime = endDate.toISOString();
    }

    // If there's more data to process or we need to advance the date
    if (hasMore || (savedQuery.dateInterval && nextEndTime <= new Date().toISOString())) {
      // Update the savedQuery document with the new offset and dates
      await modifyDoc("savedQueries", savedQuery.id, {
        lastOffset: currentOffset,
        status: "running",
        startTime: nextStartTime,
        endTime: nextEndTime
      }, true);

      // Schedule the next batch to run
      const nextJob = {
        savedQuery: {
          ...savedQuery,
          startTime: nextStartTime,
          endTime: nextEndTime
        },
        type: "append",
        offset: currentOffset
      };
      await runUpdateBigQueryTask(nextJob);

      console.log(`Scheduled next batch starting at offset ${currentOffset} with dates ${nextStartTime} to ${nextEndTime}`);
    } else {
      // Reset the offset and update status since we've completed processing
      await modifyDoc("savedQueries", savedQuery.id, {
        lastOffset: 0,
        lastRun: new Date(),
        status: "completed",
        startTime: nextStartTime,
        endTime: nextEndTime
      }, true);
      console.log("Completed processing all data");
    }
  }
  return;
};
const runUpdateBigQueryTask = async (job) => {
  if (process.env.FUNCTIONS_EMULATOR) {
    await updateBigQueryTableFromSuiteQl(job);
  } else {
    await createTask("netsuiteTaskQueue", job, TASK_URLS.updateBigQueryTable);
  }
  return true;
};
const bigQueryMergeTempTable = async (datasetId, tableId, tempDatasetId, tempTableId, mergeKey, options = {}) => {
  const dataset = bigquery.dataset(datasetId);
  const table = dataset.table(tableId);
  const tempDataset = bigquery.dataset(tempDatasetId);
  const tempTable = tempDataset.table(tempTableId);

  try {
    // Get the schema of the target table
    const [tableMetadata] = await table.getMetadata();
    const schema = tableMetadata.schema;

    // Get all field names except the merge keys for the update clause
    const fields = schema.fields
      .map(field => field.name)
      .filter(fieldName => !["internal_id", "po_line_id", "items_line_id"].includes(fieldName));

    // Build the update clause for matched records
    const updateSet = fields.map(field => `T.${field} = S.${field}`).join(",\n      ");

    // Build the insert clause for new records
    const allFields = schema.fields.map(field => field.name);
    const insertFields = allFields.join(", ");
    const insertValues = allFields.map(field => `S.${field}`).join(", ");

    // Delete rows from target table where internal_id exists in source but the composite key combination no longer exists
    const deleteObsoleteQuery = `
      DELETE FROM \`${projectId}.${datasetId}.${tableId}\` T
      WHERE T.internal_id IN (
        SELECT DISTINCT internal_id
        FROM \`${projectId}.${tempDatasetId}.${tempTableId}\`
      )
      AND NOT EXISTS (
        SELECT 1
        FROM \`${projectId}.${tempDatasetId}.${tempTableId}\` S
        WHERE T.internal_id = S.internal_id
          AND T.po_line_id = S.po_line_id
          AND T.items_line_id = S.items_line_id
      )
    `;

    // Execute the delete operation for obsolete rows
    const [deleteObsoleteJob] = await bigquery.createQueryJob({ query: deleteObsoleteQuery });
    await deleteObsoleteJob.getQueryResults();

    // Build and execute the MERGE query using composite key
    const query = `
      MERGE \`${projectId}.${datasetId}.${tableId}\` T
      USING \`${projectId}.${tempDatasetId}.${tempTableId}\` S
      ON T.internal_id = S.internal_id 
        AND T.po_line_id = S.po_line_id 
        AND T.items_line_id = S.items_line_id
      WHEN MATCHED THEN
        UPDATE SET
          ${updateSet}
      WHEN NOT MATCHED THEN
        INSERT (${insertFields})
        VALUES (${insertValues})
    `;

    // Add optional clauses if provided
    const finalQuery = options.whenMatchedCondition
      ? query.replace("WHEN MATCHED THEN", `WHEN MATCHED AND ${options.whenMatchedCondition} THEN`)
      : query;

    console.log(`Executing merge from ${tempTable} into ${tableId} using composite key`);
    const [job] = await bigquery.createQueryJob({ query: finalQuery });
    const [results] = await job.getQueryResults();

    console.log(`Merge completed successfully`);
    return results;
  } catch (error) {
    console.error(`Error merging tables: ${error.message}`);
    throw error;
  }
};
const bigQueryDeleteDuplicates = async (datasetId, tableId, uniqueKey) => {
  try {
    // Query to identify and delete duplicate rows based on the uniqueKey
    // This approach keeps the row with the highest (most recent) insertion ID
    const query = `
      DELETE FROM \`${projectId}.${datasetId}.${tableId}\`
      WHERE STRUCT(${sanitizeFieldName(uniqueKey)}) IN (
        SELECT STRUCT(${sanitizeFieldName(uniqueKey)})
        FROM (
    SELECT 
      ${sanitizeFieldName(uniqueKey)},
      ROW_NUMBER() OVER (
        PARTITION BY ${sanitizeFieldName(uniqueKey)} 
      ) AS row_num
    FROM \`${projectId}.${datasetId}.${tableId}\`
  )
  WHERE row_num > 1
)

    `;

    console.log(`Executing duplicate deletion in ${datasetId}.${tableId} using key ${uniqueKey}`);
    const [job] = await bigquery.createQueryJob({ query });
    const results = await job.getQueryResults();

    console.log(`Successfully deleted duplicate rows from ${datasetId}.${tableId}`);
    return results;
  } catch (error) {
    console.error(`Error deleting duplicates: ${error.message}`);
    throw error;
  }
};
const bigQueryDeleteRows = async (datasetId, tableId, uniqueKey, uniqueKeyValues) => {
  const dataset = bigquery.dataset(datasetId);
  const table = dataset.table(tableId);

  try {
    // Get table metadata to determine the column type
    const [tableMetadata] = await table.getMetadata();
    const schema = tableMetadata.schema;
    const uniqueKeyField = schema.fields.find(field => field.name === sanitizeFieldName(uniqueKey));

    if (!uniqueKeyField) {
      throw new Error(`Column ${uniqueKey} not found in table schema`);
    }

    // Format values based on the column type
    const formattedValues = uniqueKeyValues.map(value => {
      switch (uniqueKeyField.type) {
        case "INTEGER":
        case "INT64":
          // Ensure numeric values aren't quoted
          return Number(value);
        case "FLOAT":
        case "FLOAT64":
          return Number(value);
        case "BOOLEAN":
          return value === "true" || value === true ? "true" : "false";
        case "STRING":
        default:
          // Escape single quotes in string values
          return `'${String(value).replace(/'/g, "\\'")}'`;
      }
    });

    const query = `
      DELETE FROM \`${projectId}.${datasetId}.${tableId}\`
      WHERE ${sanitizeFieldName(uniqueKey)} IN (${formattedValues.join(",")})
    `;

    const [job] = await bigquery.createQueryJob({ query });
    const results = await job.getQueryResults();
    console.log(`Successfully deleted rows from ${datasetId}.${tableId}`);
    return results;
  } catch (error) {
    console.error(`Error deleting rows: ${error.message}`);
    throw error;
  }
};


const bigQueryAppendRowsWithLoadJob = async (datasetId, tableId, rows) => {
  if (!rows || !rows.length) {
    console.log("No rows provided for append.");
    return { success: true, message: "No rows to append" };
  }

  let tmpFilePath = null;
  
  try {
    // Apply testing dataset logic
    const actualDatasetId = getTestingDataset(datasetId);
    
    const dataset = bigquery.dataset(actualDatasetId);
    const table = dataset.table(tableId);

    // Check if table exists
    const [tableExists] = await table.exists();
    if (!tableExists) {
      throw new Error(`Table ${actualDatasetId}.${tableId} does not exist`);
    }

    // For demand_plan table, remove duplicates before inserting
    if (tableId === "demand_plan") {
      console.log("Removing potential duplicates for demand_plan table...");
      const uniqueKeys = new Set();
      const filteredRows = rows.filter(row => {
        const key = `${row.upc}_${row.forecast_node}_${row.date}`;
        if (uniqueKeys.has(key)) {
          console.log(`Duplicate row filtered: ${key}`);
          return false;
        }
        uniqueKeys.add(key);
        return true;
      });
      
      if (filteredRows.length !== rows.length) {
        console.log(`Filtered ${rows.length - filteredRows.length} duplicate rows from batch`);
        rows = filteredRows;
      }
      
      if (rows.length === 0) {
        console.log("All rows were duplicates, skipping insert");
        return { success: true, message: "All rows were duplicates, no insertion needed" };
      }
    }

    // Get table schema to ensure data compatibility
    const [tableMetadata] = await table.getMetadata();
    const schema = tableMetadata.schema;

    // Format rows according to schema
    const formattedRows = formatRows({ schema, rows });

    if (!formattedRows || formattedRows.length === 0) {
      throw new Error("No valid rows to insert after formatting");
    }

    // Create a temporary file with the data in NEWLINE_DELIMITED_JSON format
    const tmpDir = os.tmpdir();
    tmpFilePath = path.join(tmpDir, `bigquery-load-${Date.now()}.json`);

    // Stream the array of objects to NDJSON to avoid building a huge string in memory
    const writeStream = fs.createWriteStream(tmpFilePath, { encoding: "utf8" });
    try {
      for (const row of formattedRows) {
        writeStream.write(JSON.stringify(row) + "\n");
      }
    } finally {
      await new Promise((resolve, reject) => {
        writeStream.end();
        writeStream.on("finish", resolve);
        writeStream.on("error", reject);
      });
    }

    // Define load job options
    const options = {
      sourceFormat: "NEWLINE_DELIMITED_JSON",
      writeDisposition: "WRITE_APPEND", // Append to existing table
      schema: schema
    };

    console.log(`Starting load job to append ${rows.length} rows to ${actualDatasetId}.${tableId}...`);

    // Execute the load job
    const [job] = await table.load(tmpFilePath, options);
    console.log(`Load job ${job.id} created.`);
    console.log(`Load job ${job.id} completed successfully. ${formattedRows.length} rows appended.`);

    return { 
      success: true, 
      message: `Successfully appended ${formattedRows.length} rows`,
      jobId: job.id
    };
  } catch (error) {
    console.error(`Error appending rows with load job: ${error.message}`, error.stack);
    throw error;
  } finally {
    // Always clean up the temporary file
    if (tmpFilePath && fs.existsSync(tmpFilePath)) {
      try {
        fs.unlinkSync(tmpFilePath);
        console.log("Temporary file cleaned up successfully");
      } catch (cleanupError) {
        console.error("Error cleaning up temporary file:", cleanupError.message);
      }
    }
  }
};
exports.bigQueryDeleteDuplicates = bigQueryDeleteDuplicates;
exports.bigQueryMergeTempTable = bigQueryMergeTempTable;
exports.bigQueryAppendRowsWithLoadJob = bigQueryAppendRowsWithLoadJob;
exports.bigQueryDeleteRows = bigQueryDeleteRows;


// Add this new function to sanitize field names
const sanitizeFieldName = (fieldName) => {
  // Replace slashes, spaces and other invalid characters with underscores
  return fieldName
    // eslint-disable-next-line no-useless-escape
    .replace(/[\/\s\(\)\+\-\*\?\[\]\{\}\^\$\\\.\,\:\;\'\"\!\@\#\%\&\=]/g, "_")
    .replace(/__+/g, "_") // Replace multiple consecutive underscores with a single one
    .replace(/^_|_$/g, "") // Remove leading and trailing underscores
    .replace(/\s+/g, "_") // replace all spaces with underscores
    .substring(0, 300); // Ensure name is not longer than 300 characters
};
exports.bigQuerySanitizeFieldName = sanitizeFieldName;

// Update the buildSchemaFromRows function to sanitize field names
const buildSchemaFromRows = (rows) => {
  const schema = [];
  //convert to int or float if possible
  const columns = rows.map(r => Object.keys(r));
  const uniqueColumns = [...new Set(columns.flat())];
  uniqueColumns.forEach(key => {
    const sanitizedKey = sanitizeFieldName(key);
    const rowValues = rows.map(r => r[key]);
    const uniqueRowValues = [...new Set(rowValues)].filter(v => v !== null && v !== undefined && v !== "" && v !== "null" && v !== "NULL" && v !== "NaN");

    //convert to date if possible
    if (uniqueRowValues.every(x => x === null)) {
      schema.push({ name: sanitizedKey, type: "STRING" });
    } else if (uniqueRowValues.every(v => typeof v === "boolean" || v === "No" || v === "Yes")) {
      schema.push({ name: sanitizedKey, type: "BOOL" });
    } else if (uniqueRowValues.every(v => !isNaN(Number(v)) && Number.isInteger(Number(v)))) {
      schema.push({ name: sanitizedKey, type: "INTEGER" });
    } else if (uniqueRowValues.every(v => !isNaN(Number(v)) && !Number.isInteger(Number(v)))) {
      schema.push({ name: sanitizedKey, type: "FLOAT64" });
    } else if (uniqueRowValues.every(v => typeof v === "string" && !isNaN(Date.parse(v)) && new Date(Date.parse(v)).getFullYear() > 2000)) {
      //check if dates have a time component if not then it's a date if it does then it's a datetime
      // Check if the date strings represent dates without time components
      if (uniqueRowValues.every(v => {
        const date = new Date(Date.parse(v));
        return date.getHours() === 0 && date.getMinutes() === 0 && date.getSeconds() === 0 && date.getMilliseconds() === 0;
      })) {
        schema.push({ name: sanitizedKey, type: "DATE" });
      } else {
        schema.push({ name: sanitizedKey, type: "DATETIME" });
      }
    } else {
      schema.push({ name: sanitizedKey, type: "STRING" });
    }
  });
  return schema;
};
const formatRows = ({ schema, rows }) => {
  return rows.map(row => {
    const formattedRow = {
      // updatedAt: new Date().toISOString()
    };

    const forceStringFields = ["upc", "upc_code", "barcode"];

    schema.fields.filter(f => Object.keys(row).includes(f.name)).forEach(field => {
      const fieldName = field.name;
      const fieldType = field.type;
      const value = row[fieldName];

      if (forceStringFields.includes(fieldName.toLowerCase())) {
        formattedRow[fieldName] = value;
      } else {
        // try {
        switch (fieldType) {
          case "BOOLEAN":
            // Convert various boolean representations
            if (value === null || value === undefined || value === "") {
              formattedRow[fieldName] = false;
            } else if (typeof value === "boolean") {
              formattedRow[fieldName] = value;
            } else if (value === "Yes" || value === "yes" || value === "true" || value === "TRUE" || value === "1") {
              formattedRow[fieldName] = true;
            } else if (value === "No" || value === "no" || value === "false" || value === "FALSE" || value === "0") {
              formattedRow[fieldName] = false;
            } else {
              formattedRow[fieldName] = false;
            }
            break;

          case "INTEGER":
            if (value === null || value === undefined || value === "") {
              formattedRow[fieldName] = 0;
            } else {
              formattedRow[fieldName] = Number.isInteger(Number(value)) ? Number(value) : 0;
            }
            break;

          case "FLOAT":
            if (value === null || value === undefined || value === "") {
              formattedRow[fieldName] = 0;
            } else {
              formattedRow[fieldName] = !isNaN(Number(value)) ? Number(value) : 0;
            }
            break;
          case "DATE":
            if (value === null || value === undefined || value === "") {
              formattedRow[fieldName] = null;
            } else {
              const date = new Date(Date.parse(value));
              formattedRow[fieldName] = date.toISOString().split("T")[0];
            }
            break;
          case "DATETIME":
            if (value === null || value === undefined || value === "") {
              formattedRow[fieldName] = null;
            } else {
              const date = new Date(Date.parse(value));
              formattedRow[fieldName] = date.toISOString().replace("Z", "");
            }
            break;
          case "STRING":
          default:
            formattedRow[fieldName] = value;
            break;
        }
      }
    });

    return formattedRow;
  });
};
const bigQueryAppendRows = async (datasetId, tableId, rows, schema = null) => {
  if (!rows || !rows.length) {
    console.log("No rows provided for append.");
    return;
  }
  // Insert rows into the table
  try {
    const dataset = bigquery.dataset(datasetId);
    let table = dataset.table(tableId);
    const [exists] = await table.exists();
    if (!exists) {
      await bigQueryCreateDataset(datasetId);
      //check if table exists and create if it doesn't
      table = await bigQueryCreateTable(datasetId, tableId, rows, schema);
    }
    // Format rows according to BigQuery requirements
    const formattedRows = formatRows({ schema, rows });
    const [results] = await table.insert(formattedRows);
    console.log(`Successfully inserted ${rows.length} rows into ${datasetId}.${tableId}`);
    return results;
  } catch (error) {
    console.error(`Error appending rows to ${datasetId}.${tableId}:`, error);
    throw error;
  }
};
// const mergeRows = async (datasetId, tableId, rows, schema) => {
//   const fields = Object.keys(rows[0]).filter(f => f !== "Line_Unique_Key");
//   const updateSet = fields.map(f => `        ${f} = S.${f}`).join(",\n");
//   const insertFields = [...fields, "Line_Unique_Key"].join(", ");
//   const insertValues = [...fields.map(f => `S.${f}`), "S.Line_Unique_Key"].join(", ");

//   const query = `
//     MERGE \`${datasetId}.${tableId}\` T
//     USING UNNEST(@rows) AS S
//     ON T.Line_Unique_Key = S.Line_Unique_Key

//     WHEN MATCHED THEN
//       UPDATE SET
//         ${updateSet}
//     WHEN NOT MATCHED THEN
//       INSERT (${insertFields})
//       VALUES (${insertValues});
//   `;

//   const options = {
//     query,
//     params: { rows: rows, types: { rows: schema.fields.map(field => ({ [field.name]: field.type })) } },
//   };

//   const [job] = await bigquery.createQueryJob(options);
//   await job.getQueryResults();
//   console.log("Merge complete");
// };

const upsertRows = async (datasetId, tableId, uniqueKey, rows) => {
  if (!rows || !rows.length) {
    console.log("No rows provided for upsert.");
    return;
  }
  // const tempDatasetId = "temp";

  // Apply testing dataset logic
  const actualDatasetId = getTestingDataset(datasetId);

  try {
    // Create dataset
    const dataset = await bigQueryCreateDataset(actualDatasetId);
    const targetTable = dataset.table(tableId);
    const [tableExists] = await targetTable.exists();

    if (!tableExists) {
      // If table doesn't exist, just create and load it directly
      console.log(`Table ${tableId} does not exist. Creating and loading directly.`);
      await bigQueryCreateTable(actualDatasetId, tableId, rows);
      const normalizedRows = formatRows({ schema: buildSchemaFromRows(rows), rows });
      await bigQueryAppendRowsWithLoadJob(actualDatasetId, tableId, normalizedRows);
      console.log(`Successfully created and loaded ${rows.length} rows into ${actualDatasetId}.${tableId}`);
      return true;
    }

    // Get schema from existing table
    // const [targetTableMetadata] = await targetTable.getMetadata();
    // const schema = targetTableMetadata.schema;
    //delete rows with an existing unique key
    await bigQueryDeleteRows(actualDatasetId, tableId, uniqueKey, rows.map(row => row[uniqueKey]));
    //append rows
    await bigQueryAppendRowsWithLoadJob(actualDatasetId, tableId, rows);
    console.log(`Successfully upserted ${rows.length} rows into ${actualDatasetId}.${tableId}`);
    return true;
  } catch (error) {
    console.error(`Error upserting rows to ${actualDatasetId}.${tableId}:`, error);
    throw error;
  }
};

const addColumn = async (datasetId, tableId, newColumnName, newColumnType, options = {}) => {
  let query;
  if (newColumnName && newColumnType) {
    query = `ALTER TABLE \`${projectId}.${datasetId}.${tableId}\` ADD COLUMN ${newColumnName} ${newColumnType};`;
    if (options) {
      query += `ALTER TABLE \`${projectId}.${datasetId}.${tableId}\` ALTER COLUMN ${newColumnName} 
        SET OPTIONS (${Object.keys(options).map(option => `${option} = "${options[option]}"`).join(", ")});`;
    }
  }
  try {
    return await runQuery({ query });
  } catch (error) {
    console.error("Error adding column", error.message);
    return error.message;
  }
};

const editColumn = async (datasetId, tableId, columnName, newColumnName, newColumnType = null, options = {}) => { // TODO: implement options
  const tempColumnName = `temp_${columnName}`;
  const proms = [];
  let query;
  if (newColumnType) {
    query = `ALTER TABLE \`${projectId}.${datasetId}.${tableId}\` RENAME COLUMN ${columnName} TO ${tempColumnName};`;
    proms.push(runQuery({ query }));
    query = `ALTER TABLE \`${projectId}.${datasetId}.${tableId}\` ADD COLUMN ${columnName} ${newColumnType};`;
    proms.push(runQuery({ query }));
    query = `UPDATE \`${projectId}.${datasetId}.${tableId}\` SET ${columnName} = CAST(${tempColumnName} AS ${newColumnType}) WHERE TRUE;`;
    proms.push(runQuery({ query }));
    query = `ALTER TABLE \`${projectId}.${datasetId}.${tableId}\` DROP COLUMN ${tempColumnName};`;
    proms.push(runQuery({ query }));
  }

  if (columnName != newColumnName) {
    query = `ALTER TABLE \`${projectId}.${datasetId}.${tableId}\` RENAME COLUMN ${columnName} TO ${newColumnName};`;
    proms.push(runQuery({ query }));
  }
  try {
    return await Promise.all(proms);
  } catch (error) {
    console.error("Error editing column", error.message);
    return error.message;
  }
};

const deleteColumn = async (datasetId, tableId, columnName) => {
  const query = `ALTER TABLE \`${projectId}.${datasetId}.${tableId}\` DROP COLUMN ${columnName};`;
  try {
    return await runQuery({ query });
  } catch (error) {
    console.error("Error deleting column", error.message);
    return error.message;
  }
};

// Helper function to replace dataset references in queries for testing
const replaceDatasetInQuery = (query) => {
  // Check if we're running on localhost
  // const isLocalhost = process.env.FUNCTIONS_EMULATOR === "true" || 
  //                    process.env.NODE_ENV === "development" ||
  //                    process.env.LOCALHOST === "true";
  
  // if (isLocalhost) {
  //   // Simple and safe approach: only replace fully qualified table names
  //   // This avoids breaking SQL syntax while still handling the main use case
  //   return query.replace(
  //     /`hj-reporting\.([a-zA-Z_][a-zA-Z0-9_]*)\.([a-zA-Z_][a-zA-Z0-9_]*)`/g,
  //     "`hj-reporting.testing_$1.$2`"
  //   );
  // }
  return query;
};

const runQuery = async (options) => {
  const results = [];
  try {
    console.log("Running query", options);
    // Apply testing dataset logic to the query
    options.query = replaceDatasetInQuery(options.query);
    options.query = options.query.replaceAll("\n", " ");
    //if deleting rows from the query then save a snapshot of the table
    if (options.query.includes("DELETE")) {
      // Extract datasetId and tableId from the query string (expects format: `hj-reporting.datasetId.tableId`)
      const match = options.query.match(/`([\w-]+)\.([\w-]+)\.([\w-]+)`/);
      if (match) {
        const datasetId = match[2];
        const tableId = match[3];
        await bigQueryCreateSnapshotTable(datasetId, tableId);
      } else {
        console.warn("Could not extract datasetId and tableId from query for snapshot.");
      }
    }

    const [job] = await bigquery.createQueryJob(options);
    console.log(`Job ${job.id} started.`);
    // This line waits for the BigQuery job to complete and retrieves the results.
    // job.completeListeners() returns a promise that resolves when the job is done,
    // and the destructuring assignment [resObj] extracts the first element of the returned array,
    // which contains the query results and metadata.
    const resObj = await job.getQueryResults();
    const [rows] = resObj;
    console.log("Rows:", rows.length);
    if (rows.length === 0) {
      return [];
    }
    // console.log("Rows:", rows);
    rows.forEach((row) => results.push(row));
    console.log("Results:", results.length);
    return results;
  } catch (error) {
    console.error("Error running query data", error.message, error.stack);
    throw error;
  }
};

const bigQueryDeleteTable = async (datasetId, tableId) => {
  try {
    // Apply testing dataset logic
    const actualDatasetId = getTestingDataset(datasetId);
    
    const dataset = bigquery.dataset(actualDatasetId);
    const table = dataset.table(tableId);
    await table.delete();
    console.log(`Table ${tableId} deleted from ${actualDatasetId}`);
    return true;
  } catch (error) {
    console.error("Error deleting table", error.message);
    throw error;
  }
};

const buildSchemaFromHeaders = (headers) => {
  return headers.map(header => {
    const match = header.match(/^(.+?)\((\w+)\)$/);
    if (!match) {
      throw new Error(`Invalid header format: ${header}`);
    }
    const rawName = match[1].trim();
    const type = match[2].toUpperCase().trim();
    return { name: sanitizeFieldName(rawName), type };
  });
};

const getHeadersFromGcsFile = async (bucketName, filePath) => {
  const file = storage.bucket(bucketName).file(filePath);
  const [contents] = await file.download();
  const parsed = parse(contents.toString(), { to_line: 1 });
  return parsed[0]; // First row is header
};

const bigQueryUploadCsvFiles = async ({ datasetId, tableId, bucketName, prefix }) => {
  try {
    // Apply testing dataset logic
    const actualDatasetId = getTestingDataset(datasetId);
    
    console.log("Uploading CSV files to BigQuery", actualDatasetId, tableId, bucketName, prefix);

    // const outputFileName = await combineCsvFilesInGcs({
    //   bucketName: bucketName,
    //   prefix: prefix,
    //   outputPrefix: "cb_netsuite_transactions/"
    // });
    // const gcsUris = storage.bucket(bucketName).file(outputFileName);

    const gcsFiles = await storageGetCsvFiles({ bucketName, prefix });
    const gcsUris = gcsFiles.map(f => storage.bucket(bucketName).file(f.name));

    if (!gcsUris.length) {
      console.log("No CSV files found");
      return;
    }

    console.log("GCS URIs:", gcsUris);

    // Extract schema from the first file
    const headers = await getHeadersFromGcsFile(bucketName, gcsFiles[0].name);
    const schema = buildSchemaFromHeaders(headers);

    const metadata = {
      sourceFormat: "CSV",
      skipLeadingRows: 1,
      writeDisposition: "WRITE_TRUNCATE",
      schema: { fields: schema },
    };

    const [job] = await bigquery
      .dataset(actualDatasetId)
      .table(tableId)
      .load(gcsUris, metadata);

    console.log(`BigQuery job ${job.id} started.`);

    if (job.getMetadata) {
      const [jobResult] = await job.getMetadata();
      if (jobResult.status.errors && jobResult.status.errors.length > 0) {
        console.error("BigQuery job completed with errors:", jobResult.status.errors);
        throw new Error(JSON.stringify(jobResult.status.errors));
      }
    }

    console.log("CSV files successfully loaded into BigQuery.");
    return job;
  } catch (error) {
    console.error("Error uploading CSV files to BigQuery:", error.message, error.stack);
    throw error;
  }
};

const bigQueryGetItems = async ({whereClause = ""}) => {
  const query = `
    SELECT * FROM \`hj-reporting.items.items_netsuite\`
    ${whereClause ? `WHERE ${whereClause}` : ""}
  `;
  return await runQuery({ query });
};

exports.bigQueryGetItems = bigQueryGetItems;
exports.bigQueryUploadCsvFiles = bigQueryUploadCsvFiles;
exports.upsertRows = upsertRows;
exports.addColumn = addColumn;
exports.editColumn = editColumn;
exports.deleteColumn = deleteColumn;
exports.runQuery = runQuery;
exports.bigQueryCreateDataset = bigQueryCreateDataset;
exports.bigQueryCreateTable = bigQueryCreateTable;
exports.bigQueryReplaceTable = bigQueryReplaceTable;
exports.updateBigQueryReports = updateBigQueryReports;
exports.bigQueryGetDatasetsAndTables = bigQueryGetDatasetsAndTables;
exports.bigQueryRunQueries = bigQueryRunQueries;
exports.bigQueryAppendRows = bigQueryAppendRows;
exports.updateBigQueryTableFromSuiteQl = updateBigQueryTableFromSuiteQl;
exports.bigQueryDeleteTable = bigQueryDeleteTable;
exports.bigQueryLoadToTable = bigQueryLoadToTable;
exports.bigQueryCreateSnapshotTable = bigQueryCreateSnapshotTable;
exports.getTestingDataset = getTestingDataset;