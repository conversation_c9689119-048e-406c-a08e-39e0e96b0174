import React, { useState, useEffect, useMemo } from 'react';
import { Card, Button, message, Typography } from 'antd';
import { AgGridReact } from 'ag-grid-react';
import { api } from './firebase';
import { themeBalham } from 'ag-grid-community';

import { SyncOutlined, DownloadOutlined } from '@ant-design/icons';

const { Text } = Typography;


const ShippingExceptionReport = ({ userObj }) => {
  const [rowData, setRowData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedRows, setSelectedRows] = useState([]);
  const [gridApi, setGridApi] = useState(null);
  const [processingRows, setProcessingRows] = useState(new Set());

  // Check if user has edit permissions
  const hasEditPermission = useMemo(() => {
    return userObj?.userPermissions?.some(
      permission => permission.technicalName === 'edit:shippingExceptions' && permission.hasAccess
    );
  }, [userObj]);

  // Calculate summary statistics
  const summaryStats = useMemo(() => {
    const total = rowData.length;
    const shipped = rowData.filter(row => row.is_shipped).length;
    const withPackageId = rowData.filter(row => row.easypost_package_id && row.easypost_package_id.trim() !== '').length;
    return { total, shipped, withPackageId };
  }, [rowData]);

  const columnDefs = [
    { 
      field: 'is_shipped',
      headerName: 'Shipped',
      sortable: true,
      filter: true,
      cellRenderer: (params) => {
        const isProcessing = processingRows.has(params.data.easypost_package_id);
        const hasPackageId = params.data.easypost_package_id && params.data.easypost_package_id.trim() !== '';
        
        if (!hasPackageId) {
          return <span style={{ color: '#999' }}>No Package ID</span>;
        }

        if (!hasEditPermission) {
          return <span>{params.value ? 'Shipped' : 'Not Shipped'}</span>;
        }

        return (
          <Button
            type={params.value ? 'primary' : 'default'}
            onClick={() => handleMarkShipped(params.data)}
            loading={isProcessing}
            disabled={isProcessing}
          >
            {params.value ? 'Mark as Unshipped' : 'Mark as Shipped'}
          </Button>
        );
      }
    },
    { field: 'created_from', headerName: 'Created From', sortable: true, filter: true },
    { field: 'po_check_number', headerName: 'PO/Check Number', sortable: true, filter: true },
    { field: 'document_number', headerName: 'Document Number', sortable: true, filter: true },
    { field: 'easypost_package_id', headerName: 'Easypost Package ID', sortable: true, filter: true },
    { field: 'carrier', headerName: 'Carrier', sortable: true, filter: true },
    { field: 'service_level', headerName: 'Service Level', sortable: true, filter: true },
    { field: 'tracking_number', headerName: 'Tracking Number', sortable: true, filter: true },
    { field: 'status', headerName: 'Status', sortable: true, filter: true },
    { field: 'batch_number', headerName: 'Batch #', sortable: true, filter: true }
  ];

  const defaultColDef = {
    flex: 1,
    minWidth: 100,
    resizable: true,
  };

  const rowSelection = useMemo(() => {
    return {
      mode: hasEditPermission ? 'multiRow' : undefined,
      checkboxSelection: true,
      headerCheckboxSelection: true,
      isRowSelectable: (node) => {
        // I don't know why this returns null
        const selectable = node.data.easypost_package_id && node.data.easypost_package_id.trim() !== '';
        if (!selectable) {
          return false;
        }
        return true;
      },
      hideDisabledCheckboxes: true,
    };
  }, [hasEditPermission]);

  const getRowStyle = (params) => {
    if (!params.data.easypost_package_id || params.data.easypost_package_id.trim() === '') {
      return { background: '#f5f5f5', color: '#FF0000' }; // Gray background for rows without package ID
    }
    if (params.data.is_shipped) {
      return { background: '#f6ffed' }; // Light green background for shipped items
    }
    return null;
  };

  const loadShippingData = async () => {
    try {
      setLoading(true);
      const query = `SELECT * FROM \`hj-reporting.shipping.shipping_exceptions\``;
      const result = await api.bigQueryRunQueryOnCall({ options: { query } });
      setRowData(result.data);
    } catch (error) {
      message.error('Failed to load shipping data');
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const handleMarkShipped = async (row) => {
    if (!hasEditPermission) return;
    if (!row.easypost_package_id || row.easypost_package_id.trim() === '') return;
    if (processingRows.has(row.easypost_package_id)) return;

    try {
      setProcessingRows(prev => new Set([...prev, row.easypost_package_id]));
      const query = `
        UPDATE \`hj-reporting.shipping.shipping_exceptions\`
        SET is_shipped = ${!row.is_shipped}
        WHERE easypost_package_id = '${row.easypost_package_id}'
      `;
      await api.bigQueryRunQueryOnCall({ options: { query } });
      message.success(`Successfully ${row.is_shipped ? 'unmarked' : 'marked'} as shipped`);
      loadShippingData(); // Refresh the data
    } catch (error) {
      message.error('Failed to update shipping status');
      console.error(error);
    } finally {
      setProcessingRows(prev => {
        const newSet = new Set(prev);
        newSet.delete(row.easypost_package_id);
        return newSet;
      });
    }
  };

  const handleBulkMarkShipped = async () => {
    if (!hasEditPermission) return;
    if (selectedRows.length === 0) {
      message.warning('Please select at least one row to mark as shipped');
      return;
    }

    // Filter out rows without package IDs
    const validRows = selectedRows.filter(row => row.easypost_package_id && row.easypost_package_id.trim() !== '');
    if (validRows.length === 0) {
      message.warning('No valid rows selected (missing package IDs)');
      return;
    }

    try {
      setLoading(true);
      const packageIds = validRows.map(row => `'${row.easypost_package_id}'`).join(',');
      const query = `
        UPDATE \`hj-reporting.shipping.shipping_exceptions\`
        SET is_shipped = NOT is_shipped
        WHERE easypost_package_id IN (${packageIds})
      `;
      await api.bigQueryRunQueryOnCall({ options: { query } });
      message.success(`Successfully toggled shipping status for ${validRows.length} items`);
      loadShippingData(); // Refresh the data
      setSelectedRows([]); // Clear selection
    } catch (error) {
      message.error('Failed to update shipping status');
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const handleSyncData = async () => {
    if (!hasEditPermission) return;
    try {
      setLoading(true);
      const result = await api.syncShippingExceptionsOnCall();
      if (result.data.success) {
        message.success(`Successfully synced ${result.data.count} shipping records`);
        loadShippingData(); // Refresh the data after sync
      } else {
        message.error('Failed to sync shipping data');
      }
    } catch (error) {
      message.error('Failed to sync shipping data');
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const handleExport = () => {
    if (!gridApi) {
      message.error('Grid not initialized');
      return;
    }

    const params = {
      fileName: 'shipping_exceptions.csv',
      columnKeys: [
        'is_shipped',
        'created_from',
        'po_check_number',
        'document_number',
        'easypost_package_id',
        'carrier',
        'service_level',
        'tracking_number',
        'status',
        'batch_number'
      ]
    };

    gridApi.exportDataAsCsv(params);
  };

  const onGridReady = (params) => {
    setGridApi(params.api);
  };

  useEffect(() => {
    loadShippingData();
  }, []);

  return (
    <Card 
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
          <span>Shipping Exception Report</span>
          <Text type="secondary">
            ({summaryStats.shipped} shipped of {summaryStats.withPackageId} with package IDs, {summaryStats.total} total)
          </Text>
        </div>
      }
      loading={loading}
      extra={
        <div style={{ display: 'flex', gap: '8px' }}>
          {hasEditPermission && selectedRows.length > 0 && (
            <Button 
              type="primary" 
              onClick={handleBulkMarkShipped}
              loading={loading}
              disabled={loading}
            >
              Toggle Shipped Status ({selectedRows.length} Selected)
            </Button>
          )}
          <Button 
            type="primary" 
            icon={<DownloadOutlined />} 
            onClick={handleExport}
            disabled={loading}
          >
            Export CSV
          </Button>
          {hasEditPermission && (
            <Button 
              type="primary" 
              icon={<SyncOutlined />} 
              onClick={handleSyncData}
              loading={loading}
              disabled={loading}
            >
              Sync Data
            </Button>
          )}
        </div>
      }
    >
      <div style={{ height: 'calc(100vh - 200px)', width: '100%' }}>
        <AgGridReact
          rowData={rowData}
          columnDefs={columnDefs}
          defaultColDef={defaultColDef}
          pagination={true}
          paginationPageSize={50}
          enableCellTextSelection={true}
          ensureDomOrder={true}
          getRowStyle={getRowStyle}
          rowSelection={rowSelection}
          onSelectionChanged={(event) => {
            setSelectedRows(event.api.getSelectedRows());
          }}
          onGridReady={onGridReady}
          theme={themeBalham}
        />
      </div>
    </Card>
  );
};

export default ShippingExceptionReport;
