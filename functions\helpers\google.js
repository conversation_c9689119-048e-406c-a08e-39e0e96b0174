const axios = require("axios");
const { Storage } = require("@google-cloud/storage");
const { CloudTasksClient } = require("@google-cloud/tasks");

const webhookUrls={
  "LATE_ORDERS": "https://chat.googleapis.com/v1/spaces/AAAAOG3Zrhs/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=HpMacCTvckYPuD02_qVCaQ4F8d80Fe7JH9nhHDZAYio",
  "ERRORS": "https://chat.googleapis.com/v1/spaces/AAAAmDodxBo/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=HcDv439O89WhrWMn8-JcOSlK9YNNQQ3nMVgfM4qMbKU",
};

const taskClient = new CloudTasksClient();
// Initialize Cloud Storage
const storage = new Storage({
  projectId: "hj-reporting",
});
// const bucketName = "bigquery"; // Replace with your actual bucket name

exports.sendGChatMessage = async (chat, text) => {
  const bodyContent = JSON.stringify({text});
  const config = {
    url: webhookUrls[chat],
    headers: {
      "Accept": "*/*",
      "Content-Type": "application/json",
    },
    method: "POST",
    data: bodyContent,
  };
  console.log("message config", config);
  try {
    if (process.env.FUNCTIONS_EMULATOR) {
      console.log("Would have sent message:", text);
      return true;
    }
    const response = await axios.request(config);
    console.log("response", response);
    return true;
  } catch (error) {
    console.error("Error sending message to Google Chat:", error);
    return false;
  }
};

exports.createTask = async (queue, payload, taskUrl, inSeconds = 0) => {
  const project = "slickjoy-hj";
  const location = "us-central1";
  const queuePath = taskClient.queuePath(project, location, queue);
  if (!taskUrl) {
    throw new Error("Task URL is required");
  }
  if (!payload) {
    throw new Error("Payload is required");
  }
  if (!queue) {
    throw new Error("Queue is required");
  }

  const task = {
    httpRequest: {
      httpMethod: "POST",
      url: taskUrl,
      headers: {
        "Content-Type": "application/json",
      },
      body: Buffer.from(JSON.stringify(payload)).toString("base64"),
    },
  };

  if (inSeconds) {
    task.scheduleTime = {
      seconds: inSeconds + Date.now() / 1000,
    };
  }

  const maxRetries = 10;
  let attempt = 0;
  while (attempt < maxRetries) {
    try {
      const response = taskClient.createTask({ parent: queuePath, task });
      const [result] = await Promise.all([response]);
      console.log(`Created task ${JSON.stringify(result)}`);
      return response;
    } catch (error) {
      if (error.message.includes("ECONNRESET")) {
        attempt++;
        console.log(`Retrying createTask, attempt ${attempt}`);
        await new Promise((resolve) => setTimeout(resolve, 1000));
      } else {
        console.error("Error creating task:", error);
        throw error;
      }
    }
  }
  throw new Error("Error creating task: Max retries reached");
};
exports.saveToCloudStorage = async (bucketName, fileName, data, contentType = "text/csv", metadata = {}, url = null) => {
  try {
    const bucket = storage.bucket(bucketName);
    const f = bucket.file(fileName);

    if (url) {
      const response = await fetch(url);
      const buffer = await response.arrayBuffer();
      await f.save(Buffer.from(buffer), {
        contentType,
        metadata: {
          ...metadata,
          exportedAt: new Date().toISOString()
        }
      });
    } else {
      await f.save(Buffer.from(data, "utf-8"), {
        contentType,
        metadata: {
          ...metadata,
          exportedAt: new Date().toISOString()
        }
      });
    }

    console.log(`Successfully saved file: ${fileName}`);
    return true;
  } catch (error) {
    console.error("Error saving to Cloud Storage:", error);
    throw error;
  }
};
exports.storageGetCsvFiles = async ({ bucketName, prefix }) => {
  //get all bucket objects
  // if (process.env.FUNCTIONS_EMULATOR) {
  //   bucketName = "hj-reporting";
  // }
  const bucketObj = storage.bucket(bucketName);
  // List all files in the folder
  const fileResp = await bucketObj.getFiles({ prefix: prefix });
  const files = fileResp[0];
  console.log("files", files);
  const csvFiles = files.filter(f => f.metadata.contentType === "text/csv");
  console.log("csvFiles", csvFiles.map(f => f.name));
  return csvFiles;
};

exports.getFromCloudStorage = async (bucketName, filePath) => {
  const bucket = storage.bucket(bucketName);
  const file = bucket.file(filePath);
  const [response] = await file.download();
  return response.toString("utf-8");
};

exports.combineCsvFilesInGcs = async ({ bucketName, prefix, outputPrefix = "cb_netsuite_transactions/" }) => {
  const bucket = storage.bucket(bucketName);
  const [files] = await bucket.getFiles({ prefix });

  if (!files.length) {
    console.log("No CSV files found to combine.");
    return null;
  }

  console.log(`Combining ${files.length} CSV files...`);

  const outputFileName = `${outputPrefix}combined_ns_transactions.csv`;
  const outputFile = bucket.file(outputFileName);
  const writeStream = outputFile.createWriteStream({ resumable: false, contentType: "text/csv" });

  for (let i = 0; i < files.length; i++) {
    const file = files[i];
    const readStream = file.createReadStream();
    let leftover = "";
    let isFirstLine = true; 

    await new Promise((resolve, reject) => {
      readStream
        .on("data", chunk => {
          const data = leftover + chunk.toString();
          const lines = data.split("\n");
          leftover = lines.pop();

          lines.forEach(line => {
            if (isFirstLine) {
              if (i === 0) {
                line = line.replace(/\s*\([^)]*\)/g, "").trim();
                writeStream.write(line + "\n");
              }
              isFirstLine = false;
              return;
            }

            writeStream.write(line + "\n");
          });
        })
        .on("end", () => {
          if (leftover) {
            if (isFirstLine) {
              if (i === 0) {
                leftover = leftover.replace(/\s*\([^)]*\)/g, "").trim();
                writeStream.write(leftover + "\n");
              }
            } else {
              writeStream.write(leftover + "\n");
            }
          }
          resolve();
        })
        .on("error", reject);
    });
  }

  writeStream.end();

  await new Promise((resolve, reject) => {
    writeStream.on("finish", resolve);
    writeStream.on("error", reject);
  });

  console.log(`Combined file written to gs://${bucketName}/${outputFileName}`);
  return outputFileName;
};

