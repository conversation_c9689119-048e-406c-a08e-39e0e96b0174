import React, { useState, useEffect, useRef } from 'react';
import { Select, Button, Typography, message, Card, Modal, Space, Popover, DatePicker, Tag, Spin, Tooltip, Popconfirm, Checkbox, Input } from 'antd';
import { LoadingOutlined, SaveOutlined, DeleteOutlined, EyeOutlined, EyeInvisibleOutlined } from '@ant-design/icons';
import ProcessQueue from '../components/ProcessQueue';
import ProcessQueueButton from '../components/ProcessQueueButton';
import useProcessQueue from '../hooks/useProcessQueue';
import { AgGridReact } from 'ag-grid-react';
import { api, db } from './firebase';
import { ORDER_ALLOCATION_STRATEGIES, SO_STATUS_COLORS } from '../constants';
import '../App.css';

// Add modern styles for updating and error rows
const rowStyles = `
        .ag-row-updating {
          background: linear-gradient(90deg, #e6f7ff 0%, #f0f9ff 100%) !important;
          border-left: 4px solid #1890ff !important;
          position: relative;
          overflow: hidden;
        }

        .ag-row-waiting-confirmation {
          background: linear-gradient(90deg, #fff7e6 0%, #fffbe6 100%) !important;
          border-left: 4px solid #fa8c16 !important;
          position: relative;
          overflow: hidden;
        }

  .ag-row-updating::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(24, 144, 255, 0.1), transparent);
    animation: shimmer 2s infinite;
  }
  
  @keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
  }
  
  .ag-row-error {
    background: linear-gradient(90deg, #fff2f0 0%, #fff1f0 100%) !important;
    border-left: 4px solid #ff4d4f !important;
    box-shadow: inset 0 0 0 1px rgba(255, 77, 79, 0.1);
  }
  
  .ag-row-success {
    background: linear-gradient(90deg, #f6ffed 0%, #f0fff0 100%) !important;
    border-left: 4px solid #52c41a !important;
    animation: successPulse 1s ease-out;
  }
  
  @keyframes successPulse {
    0% { box-shadow: 0 0 0 0 rgba(82, 196, 26, 0.4); }
    70% { box-shadow: 0 0 0 10px rgba(82, 196, 26, 0); }
    100% { box-shadow: 0 0 0 0 rgba(82, 196, 26, 0); }
  }
  
  .ag-row-updating .ag-cell {
    pointer-events: none;
    opacity: 0.8;
  }
  
  .processing-status {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    width: 100%;
    min-height: 24px;
    justify-content: center;
  }
  
  .processing-status.updating {
    background: rgba(24, 144, 255, 0.1);
    color: #1890ff;
    border: 1px solid rgba(24, 144, 255, 0.2);
  }
  
  .processing-status.error {
    background: rgba(255, 77, 79, 0.1);
    color: #ff4d4f;
    border: 1px solid rgba(255, 77, 79, 0.2);
  }
  
  .processing-status.success {
    background: rgba(82, 196, 26, 0.1);
    color: #52c41a;
    border: 1px solid rgba(82, 196, 26, 0.2);
  }
  
  .retry-button {
    background: #ff4d4f;
    border: none;
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 11px;
    cursor: pointer;
    transition: all 0.2s;
    white-space: nowrap;
    flex-shrink: 0;
  }
  
  .retry-button:hover {
    background: #ff7875;
    transform: scale(1.05);
  }
`;

// Inject styles
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement('style');
  styleSheet.textContent = rowStyles;
  document.head.appendChild(styleSheet);
}
import { collection, onSnapshot, doc, query, where, getDocs, orderBy, addDoc, setDoc, deleteDoc, or, updateDoc, serverTimestamp } from 'firebase/firestore';
import { useUser } from '../contexts/UserContext';
import { themeBalham } from 'ag-grid-community';
import { ShareOutlined } from '@mui/icons-material';

const { Text } = Typography;

const OrderAllocation = ({ }) => {
  const { userData } = useUser();

  // Debug userData
  // console.log('👤 OrderAllocation userData:', userData); // Reduced logging noise
  const getUPCFromURL = () => {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get('upc');
  };

  // Enhanced NetSuite confirmation monitoring
  const confirmationMonitors = useRef(new Map());

  // Enhanced NetSuite confirmation monitoring with realistic timeout
  const startConfirmationMonitoring = (taskDocId, rowKey, change, timeoutMs = queueConfig?.CONFIRMATION_TIMEOUT || 180000) => {
    const monitorId = `${rowKey}_${Date.now()}`;

    const monitor = {
      taskDocId,
      rowKey,
      change,
      startTime: Date.now(),
      timeoutMs,
      isActive: true,
      healthCheckInterval: null
    };

    confirmationMonitors.current.set(monitorId, monitor);

    // Set up periodic health checks for long-running operations
    const healthCheckInterval = setInterval(async () => {
      if (!monitor.isActive) {
        clearInterval(healthCheckInterval);
        return;
      }

      const elapsed = Date.now() - monitor.startTime;
      const remainingTime = timeoutMs - elapsed;
      const elapsedMinutes = Math.floor(elapsed / 60000);

      // Proactively check for NetSuite confirmations every 30 seconds
      if (elapsed > 30000 && elapsed % 30000 < 15000) { // Every 30 seconds after initial 30 seconds
        console.log(`🔍 Checking for NetSuite confirmation: ${change.docnumber} - ${elapsedMinutes}m elapsed`);

        // Trigger a data refresh to check for NetSuite confirmations
        try {
          await triggerRefreshOpenOrders(false); // Don't show success message
        } catch (error) {
          console.error('Error refreshing data during confirmation monitoring:', error);
        }
      }

      // Log progress for long-running operations
      if (elapsed > 60000) { // After 1 minute
        console.log(`🕐 NetSuite confirmation monitoring: ${change.docnumber} - ${elapsedMinutes}m elapsed, ${Math.floor(remainingTime / 60000)}m remaining`);

        // Update task with progress indication
        try {
          await updateTask(taskDocId, {
            message: `Waiting for NetSuite confirmation - ${elapsedMinutes}m elapsed`,
            progress: Math.min(95, 60 + (elapsed / timeoutMs) * 30), // Progress from 60% to 95%
            healthCheckTimestamp: Date.now(),
            elapsedTime: elapsed
          });
        } catch (error) {
          console.error('Error updating task during health check:', error);
        }
      }
    }, 15000); // Check every 15 seconds

    monitor.healthCheckInterval = healthCheckInterval;

    // Set up main timeout with enhanced handling
    const timeoutId = setTimeout(async () => {
      if (monitor.isActive) {
        const elapsedMinutes = Math.floor((Date.now() - monitor.startTime) / 60000);
        console.warn(`⏰ NetSuite confirmation timeout for ${change.docnumber} after ${elapsedMinutes} minutes`);

        try {
          await updateTask(taskDocId, {
            status: TASK_STATUS.TIMEOUT,
            error: `NetSuite confirmation timeout after ${elapsedMinutes} minutes - API call was successful`,
            errorCategory: ERROR_CATEGORIES.TIMEOUT,
            message: `Timeout waiting for NetSuite confirmation (${elapsedMinutes}m)`,
            progress: 100,
            timeout: true,
            duration: Date.now() - monitor.startTime,
            timeoutReason: 'netsuite_confirmation_timeout'
          });
        } catch (error) {
          console.error('Error updating task on timeout:', error);
        }

        // Clear waiting state and mark as successful (API was successful)
        removeFromSet('waitingForConfirmation', rowKey);
        addToSet('successRows', rowKey);

        // Clear success state after animation
        setTimeout(() => {
          removeFromSet('successRows', rowKey);
        }, 3000);

        // Cleanup
        monitor.isActive = false;
        if (monitor.healthCheckInterval) {
          clearInterval(monitor.healthCheckInterval);
        }
        confirmationMonitors.current.delete(monitorId);
      }
    }, timeoutMs);

    monitor.timeoutId = timeoutId;

    console.log(`🔍 Set up enhanced NetSuite confirmation monitoring for ${change.docnumber} with ${Math.floor(timeoutMs / 60000)}m timeout`);
    return monitorId;
  };

  // Enhanced cleanup for confirmation monitoring
  const stopConfirmationMonitoring = (monitorId) => {
    const monitor = confirmationMonitors.current.get(monitorId);
    if (monitor) {
      monitor.isActive = false;
      if (monitor.timeoutId) {
        clearTimeout(monitor.timeoutId);
      }
      if (monitor.healthCheckInterval) {
        clearInterval(monitor.healthCheckInterval);
      }
      confirmationMonitors.current.delete(monitorId);
      console.log(`🛑 Stopped confirmation monitoring for ${monitorId}`);
    }
  };

  const clearAllConfirmationMonitors = () => {
    confirmationMonitors.current.forEach((monitor, monitorId) => {
      if (monitor.timeoutId) {
        clearTimeout(monitor.timeoutId);
      }
      if (monitor.healthCheckInterval) {
        clearInterval(monitor.healthCheckInterval);
      }
    });
    confirmationMonitors.current.clear();
  };

  // Enhanced real-time progress tracking with comprehensive notifications
  const showTaskProgressNotification = (taskId, status, messageText, progress = 0, metadata = {}) => {
    const notificationKey = `task-${taskId}`;

    // Enhanced notification content with metadata
    const getNotificationContent = () => {
      let content = `${messageText}`;

      if (progress > 0) {
        content += ` (${progress}%)`;
      }

      if (metadata.elapsedTime) {
        const elapsedMinutes = Math.floor(metadata.elapsedTime / 60000);
        if (elapsedMinutes > 0) {
          content += ` - ${elapsedMinutes}m elapsed`;
        }
      }

      if (metadata.retryCount) {
        content += ` - Retry ${metadata.retryCount}`;
      }

      return content;
    };

    switch (status) {
      case 'processing':
        message.loading({
          content: getNotificationContent(),
          key: notificationKey,
          duration: 0, // Don't auto-dismiss
        });
        break;
      case 'api_success':
        message.success({
          content: getNotificationContent(),
          key: notificationKey,
          duration: 2,
        });
        break;
      case 'waiting_confirmation':
        message.loading({
          content: `🕐 ${getNotificationContent()}`,
          key: notificationKey,
          duration: 0,
        });
        break;
      case 'completed':
        message.success({
          content: `✅ ${getNotificationContent()}`,
          key: notificationKey,
          duration: 4,
        });
        break;
      case 'failed':
        message.error({
          content: `❌ ${getNotificationContent()}`,
          key: notificationKey,
          duration: 8,
        });
        break;
      case 'timeout':
        message.warning({
          content: `⏰ ${getNotificationContent()}`,
          key: notificationKey,
          duration: 6,
        });
        break;
      case 'retrying':
        message.loading({
          content: `🔄 ${getNotificationContent()}`,
          key: notificationKey,
          duration: 0,
        });
        break;
      default:
        message.info({
          content: getNotificationContent(),
          key: notificationKey,
          duration: 3,
        });
    }
  };

  // Enhanced real-time audit logging for comprehensive tracking
  const logTaskEvent = async (taskId, event, details = {}) => {
    // Clean details object to remove undefined values
    const cleanDetails = {};
    Object.keys(details).forEach(key => {
      if (details[key] !== undefined) {
        cleanDetails[key] = details[key];
      }
    });

    const logEntry = {
      taskId,
      event,
      timestamp: serverTimestamp(),
      details: cleanDetails,
      userId: userData?.uid || 'anonymous',
      userEmail: userData?.email || 'anonymous',
      sessionId: sessionStorage.getItem('sessionId') || 'unknown',
      source: 'order_allocation_ui'
    };

    try {
      // Log to Firestore for persistence
      await addDoc(collection(db, 'taskAuditLogs'), logEntry);

      // Also log to console for development
      console.log(`📋 Task Audit Log [${event}]:`, {
        taskId,
        details,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error logging task event:', error);
    }
  };

  // Enhanced task tracking functions for robust process management
  const createTaskDocument = async (taskData) => {
    try {
      const now = Date.now();
      const taskDoc = {
        ...taskData,
        status: 'queued',
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        lastUpdatedAt: serverTimestamp(),
        userId: userData?.uid || 'unknown',

        // Enhanced tracking fields
        progress: 0,
        currentStep: 'created',
        retryCount: 0,
        maxRetries: 3,
        netsuiteRequestId: null,
        netsuiteConfirmed: false,
        confirmationTimeout: null,
        error: null,
        errorCategory: null,

        steps: {
          [`step_${now}`]: {
            step: 'created',
            timestamp: serverTimestamp(),
            message: 'Task created for order allocation change',
            progress: 0
          }
        }
      };

      const docRef = await addDoc(collection(db, 'tasks'), taskDoc);
      console.log(`Created enhanced task document: ${docRef.id} for ${taskData.description}`);

      updateMap('taskDocuments', taskData.rowKey, docRef.id);
      return docRef.id;
    } catch (error) {
      console.error('Error creating task document:', error);
      throw error;
    }
  };

  const updateTaskStatus = async (taskId, status, message, additionalData = {}) => {
    try {
      if (!taskId) {
        console.warn('No task ID provided for status update');
        return;
      }

      const now = Date.now();
      const stepKey = `step_${now}`;

      // Determine current step and progress based on status
      let currentStep = status;
      let progress = additionalData.progress || 0;

      switch (status) {
        case 'processing':
          currentStep = 'api_call_started';
          progress = Math.max(progress, 25);
          break;
        case 'api_success':
          currentStep = 'api_call_success';
          progress = Math.max(progress, 60);
          break;
        case 'waiting_confirmation':
          currentStep = 'waiting_netsuite_confirmation';
          progress = Math.max(progress, 80);
          break;
        case 'completed':
          currentStep = 'completed';
          progress = 100;
          break;
        case 'failed':
        case 'timeout':
          currentStep = 'failed';
          progress = Math.min(progress, 0); // Reset progress on failure
          break;
        case 'retrying':
          currentStep = 'retry_initiated';
          progress = 0;
          break;
      }

      const stepData = {
        step: currentStep,
        timestamp: serverTimestamp(),
        message,
        progress,
        ...additionalData
      };

      const updateData = {
        status,
        updatedAt: serverTimestamp(),
        lastUpdatedAt: serverTimestamp(),
        currentStep,
        progress,
        [`steps.${stepKey}`]: stepData,
        ...additionalData
      };

      // Handle specific status transitions with enhanced tracking
      if (status === 'completed') {
        updateData.completedAt = serverTimestamp();
        updateData.netsuiteConfirmed = true;
        updateData.duration = now - (additionalData.startedAt || now);
      }

      if (status === 'failed' || status === 'timeout') {
        updateData.completedAt = serverTimestamp();
        updateData.duration = now - (additionalData.startedAt || now);
        if (additionalData.error) {
          updateData.error = additionalData.error;
          updateData.errorCategory = categorizeError(additionalData.error);
        }
      }

      if (status === 'processing' && !additionalData.startedAt) {
        updateData.startedAt = serverTimestamp();
      }

      // Update Firestore document
      await updateDoc(doc(db, 'tasks', taskId), updateData);

      // Log audit event for comprehensive tracking
      await logTaskEvent(taskId, 'status_updated', {
        oldStatus: additionalData.previousStatus,
        newStatus: status,
        message,
        progress,
        currentStep,
        duration: updateData.duration,
        metadata: additionalData
      });

      console.log(`✅ Enhanced task update ${taskId}: ${status} - ${message} (${progress}%)`);

      // Task status is automatically displayed in the process queue via Firestore listener
      // No need for separate notifications since the process queue shows real-time status
    } catch (error) {
      console.error('❌ Error updating task status:', error);

      // Log the error for audit trail
      await logTaskEvent(taskId, 'status_update_failed', {
        error: error.message,
        attemptedStatus: status,
        message
      });
    }
  };

  // Helper function to categorize errors
  const categorizeError = (error) => {
    const errorStr = typeof error === 'string' ? error : error?.message || '';

    if (errorStr.includes('network') || errorStr.includes('fetch')) {
      return 'network';
    }
    if (errorStr.includes('timeout')) {
      return 'timeout';
    }
    if (errorStr.includes('permission') || errorStr.includes('unauthorized')) {
      return 'permission';
    }
    if (errorStr.includes('netsuite') || errorStr.includes('NetSuite')) {
      return 'netsuite_api';
    }

    return 'system';
  };

  // Enhanced row styling based on process queue status
  const getRowStyle = (params) => {
    if (!params.data) return {};

    const rowKey = `${params.data.transactionid}_${params.data.lineid}`;
    const isUpdating = taskProcessingState.updatingRows.has(rowKey);
    const hasError = taskProcessingState.errorRows.has(rowKey);
    const hasSuccess = taskProcessingState.successRows.has(rowKey);
    const isWaitingForConfirmation = taskProcessingState.waitingForConfirmation.has(rowKey);

    // Apply different background colors based on status
    if (isUpdating) {
      return {
        backgroundColor: '#e6f7ff', // Light blue for processing
        borderLeft: '4px solid #1890ff'
      };
    }

    if (isWaitingForConfirmation) {
      return {
        backgroundColor: '#f9f0ff', // Light purple for waiting
        borderLeft: '4px solid #722ed1'
      };
    }

    if (hasSuccess) {
      return {
        backgroundColor: '#f6ffed', // Light green for success
        borderLeft: '4px solid #52c41a',
        transition: 'all 0.3s ease'
      };
    }

    if (hasError) {
      return {
        backgroundColor: '#fff2f0', // Light red for error
        borderLeft: '4px solid #ff4d4f'
      };
    }

    return {};
  };

  // Enhanced NetSuite confirmation monitoring
  const monitorNetSuiteConfirmation = (task, rowKey) => {
    const taskId = task.id;
    const originalData = task.originalData;
    const updateData = task.updateData;

    if (!originalData || !updateData) {
      console.warn(`Missing data for NetSuite confirmation monitoring: ${rowKey}`);
      return;
    }

    console.log(`Starting NetSuite confirmation monitoring for ${rowKey}`);

    // Set up a listener for the specific order line to detect changes
    const orderQuery = query(
      collection(db, 'openOrders'),
      where('id', '==', originalData.id),
      where('lineid', '==', originalData.lineid)
    );

    const confirmationTimeout = setTimeout(async () => {
      console.warn(`NetSuite confirmation timeout for ${rowKey} after 30 seconds`);

      // Update task status to timeout
      await updateTaskStatus(taskId, 'timeout', 'NetSuite confirmation timeout', {
        error: 'NetSuite confirmation timeout after 30 seconds',
        errorCategory: 'timeout'
      });
    }, 30000); // 30 second timeout

    const unsubscribe = onSnapshot(orderQuery, async (snapshot) => {
      if (snapshot.empty) {
        console.log(`No order data found for confirmation monitoring: ${rowKey}`);
        return;
      }

      const currentData = snapshot.docs[0].data();

      // Check if the expected changes have been applied
      const isConfirmed = checkNetSuiteConfirmation(originalData, currentData, updateData);

      if (isConfirmed) {
        console.log(`NetSuite confirmation detected for ${rowKey}`);

        // Clear timeout
        clearTimeout(confirmationTimeout);
        unsubscribe();

        // Update task status to completed with confirmation
        await updateTaskStatus(taskId, 'completed', 'NetSuite confirmed changes successfully', {
          netsuiteConfirmed: true,
          confirmedAt: serverTimestamp(),
          confirmedData: currentData
        });

        // Note: Process queue task will be updated via processTask completion
      }
    });

    // Store cleanup function
    updateMap('processingTimeouts', `${rowKey}_confirmation_cleanup`, () => {
      clearTimeout(confirmationTimeout);
      unsubscribe();
    });
  };

  // Function to check if NetSuite has confirmed the changes
  const checkNetSuiteConfirmation = (originalData, currentData, updateData) => {
    // Check each field that was supposed to be updated
    for (const [field, newValue] of Object.entries(updateData)) {
      if (field === 'id' || field === 'lineid') continue; // Skip ID fields

      const currentValue = currentData[field];

      // Handle different data types
      if (typeof newValue === 'number') {
        if (Math.abs(currentValue - newValue) > 0.001) { // Allow for floating point precision
          console.log(`Field ${field} not yet updated: expected ${newValue}, got ${currentValue}`);
          return false;
        }
      } else if (typeof newValue === 'string') {
        if (currentValue !== newValue) {
          console.log(`Field ${field} not yet updated: expected "${newValue}", got "${currentValue}"`);
          return false;
        }
      } else {
        // For other types, use strict equality
        if (currentValue !== newValue) {
          console.log(`Field ${field} not yet updated: expected ${newValue}, got ${currentValue}`);
          return false;
        }
      }
    }

    console.log(`All fields confirmed for NetSuite update`);
    return true;
  };

  const listenToTaskUpdates = () => {
    if (!userData?.uid) return;

    const q = query(
      collection(db, 'tasks'),
      where('userId', '==', userData.uid),
      where('createdAt', '>=', new Date(Date.now() - 24 * 60 * 60 * 1000)), // Last 24 hours
      orderBy('createdAt', 'desc')
    );

    const unsubscribe = onSnapshot(q, (snapshot) => {
      const taskUpdates = new Map();

      snapshot.docs.forEach(doc => {
        const data = doc.data();
        if (data.rowKey) {
          taskUpdates.set(data.rowKey, { id: doc.id, ...data });
        }
      });

      console.log(`Received ${taskUpdates.size} task updates from Firestore`);

      // Clear states for rows that no longer have tasks in Firestore
      const currentRowKeys = new Set(taskUpdates.keys());
      const allCurrentRows = new Set([
        ...taskProcessingState.updatingRows,
        ...taskProcessingState.waitingForConfirmation,
        ...taskProcessingState.errorRows,
        ...taskProcessingState.successRows
      ]);

      allCurrentRows.forEach(rowKey => {
        if (!currentRowKeys.has(rowKey)) {
          console.log(`Clearing states for row ${rowKey} - no longer in Firestore tasks`);
          setUpdatingRows(prev => {
            const newSet = new Set(prev);
            newSet.delete(rowKey);
            return newSet;
          });
          setWaitingForConfirmation(prev => {
            const newSet = new Set(prev);
            newSet.delete(rowKey);
            return newSet;
          });
          setErrorRows(prev => {
            const newSet = new Set(prev);
            newSet.delete(rowKey);
            return newSet;
          });
          setSuccessRows(prev => {
            const newSet = new Set(prev);
            newSet.delete(rowKey);
            return newSet;
          });
          setRowErrors(prev => {
            const newMap = new Map(prev);
            newMap.delete(rowKey);
            return newMap;
          });
        }
      });

      // Update local states based on task statuses
      updateLocalStatesFromTasks(taskUpdates);
    });

    return unsubscribe;
  };

  const updateLocalStatesFromTasks = (tasks) => {
    const updating = new Set();
    const waiting = new Set();
    const errors = new Set();
    const success = new Set();
    const errorMap = new Map();

    console.log('Updating local states from Firestore tasks:', tasks.size, 'tasks');

    tasks.forEach((task, rowKey) => {
      console.log(`Task ${rowKey}: status=${task.status}, error=${task.errorMessage}`);

      switch (task.status) {
        case 'processing':
        case 'api_call':
          updating.add(rowKey);
          // Clear any previous states
          waiting.delete(rowKey);
          errors.delete(rowKey);
          success.delete(rowKey);
          errorMap.delete(rowKey);
          break;
        case 'waiting_confirmation':
          waiting.add(rowKey);
          // Clear updating state
          updating.delete(rowKey);
          errors.delete(rowKey);
          success.delete(rowKey);
          errorMap.delete(rowKey);
          break;
        case 'confirmed':
        case 'completed':
          success.add(rowKey);
          // Clear all other states
          updating.delete(rowKey);
          waiting.delete(rowKey);
          errors.delete(rowKey);
          errorMap.delete(rowKey);
          break;
        case 'failed':
        case 'timeout':
        case 'error':
          errors.add(rowKey);
          errorMap.set(rowKey, task.errorMessage || task.error || 'Unknown error');
          // Clear all other states
          updating.delete(rowKey);
          waiting.delete(rowKey);
          success.delete(rowKey);
          break;
        default:
          // For any unknown status, clear all states
          updating.delete(rowKey);
          waiting.delete(rowKey);
          errors.delete(rowKey);
          success.delete(rowKey);
          errorMap.delete(rowKey);
          break;
      }
    });

    console.log('Updated states:', {
      updating: Array.from(updating),
      waiting: Array.from(waiting),
      errors: Array.from(errors),
      success: Array.from(success)
    });

    setUpdatingRows(updating);
    setWaitingForConfirmation(waiting);
    setErrorRows(errors);
    setSuccessRows(success);
    setRowErrors(errorMap);
  };
  const gridRef = useRef();
  const [rowData, setRowData] = useState([]);
  const [originalRowData, setOriginalRowData] = useState([]);
  const [bulkStrategy, setBulkStrategy] = useState(null);
  const [hasChanges, setHasChanges] = useState(false);
  const [nodeColorMap, setNodeColorMap] = useState({});
  const [previewData, setPreviewData] = useState([]);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [isGridReady, setIsGridReady] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [bulkStartDate, setBulkStartDate] = useState(null);
  const [bulkPrepDate, setBulkPrepDate] = useState(null);
  const [bulkCancelDate, setBulkCancelDate] = useState(null);
  const [bulkIsClosed, setBulkIsClosed] = useState(false);
  // Consolidated state management for task processing
  const [taskProcessingState, setTaskProcessingState] = useState({
    updatingNetsuite: false,
    updatingRows: new Set(),
    errorRows: new Set(),
    successRows: new Set(),
    rowErrors: new Map(),
    processingTimeouts: new Map(),
    waitingForConfirmation: new Set(),
    taskDocuments: new Map(),
    isSelectiveUpdate: false,
    lastFirestoreUpdate: Date.now()
  });

  // Helper functions for state management
  const updateTaskProcessingState = (updates) => {
    setTaskProcessingState(prev => ({ ...prev, ...updates }));
  };

  const addToSet = (setName, items) => {
    setTaskProcessingState(prev => ({
      ...prev,
      [setName]: new Set([...prev[setName], ...(Array.isArray(items) ? items : [items])])
    }));
  };

  const removeFromSet = (setName, items) => {
    setTaskProcessingState(prev => {
      const newSet = new Set(prev[setName]);
      (Array.isArray(items) ? items : [items]).forEach(item => newSet.delete(item));
      return { ...prev, [setName]: newSet };
    });
  };

  const clearSet = (setName) => {
    setTaskProcessingState(prev => ({ ...prev, [setName]: new Set() }));
  };

  const updateMap = (mapName, key, value) => {
    setTaskProcessingState(prev => {
      const newMap = new Map(prev[mapName]);
      if (value === undefined) {
        newMap.delete(key);
      } else {
        newMap.set(key, value);
      }
      return { ...prev, [mapName]: newMap };
    });
  };
  const [selectedRows, setSelectedRows] = useState(0);
  const [hasActiveFilters, setHasActiveFilters] = useState(false);
  const [bulkActionsVisible, setBulkActionsVisible] = useState(false);
  const [itemOptions, setItemOptions] = useState([]);
  const [selectedNewItem, setSelectedNewItem] = useState(null);
  const gridApiRef = useRef(null);
  const [lastUpdated, setLastUpdated] = useState(null);
  const [selectedViewId, setSelectedViewId] = useState(null);
  const [searchSO, setSearchSO] = useState({
    sonumber: '',
    upc: getUPCFromURL(),
    ponumber: ''
  });
  const [openOrdersRefreshing, setOpenOrdersRefreshing] = useState(false);

  // Enhanced Process Queue with single source of truth
  const {
    tasks,
    queueVisible,
    isLoading: queueLoading,
    connectionStatus,
    addTask,
    updateTask,
    updateTaskDirect,
    processTask,
    removeTask,
    retryTask,
    handleTaskError,
    clearCompleted,
    clearAllTasks,
    toggleQueueVisibility,
    hideQueue,
    getTasksByRowKey,
    getQueueStats,
    config: queueConfig
  } = useProcessQueue(userData);


  // Saved views state
  const [savedViews, setSavedViews] = useState([]);
  // Get UPC from URL parameters if coming from Inventory Exception Report


  // Robust date parsing to handle Firestore Timestamps, ISO strings, and MM/DD/YYYY strings
  const parseToDate = (v) => {
    if (!v) return null;
    // Firestore Timestamp 
    if (v.seconds) return new Date(v.seconds * 1000);
    if (v instanceof Date) return v;
    // If it's a number (ms since epoch)
    if (typeof v === 'number') return new Date(v);
    // If it's a string try common formats
    if (typeof v === 'string') {
      // Try MM/DD/YYYY first
      const mmddyyyy = /^\s*(\d{1,2})\/(\d{1,2})\/(\d{4})\s*$/;
      const m = v.match(mmddyyyy);
      if (m) {
        const mm = Number(m[1]) - 1;
        const dd = Number(m[2]);
        const yyyy = Number(m[3]);
        return new Date(yyyy, mm, dd);
      }
      // Fallback to Date constructor (ISO etc.)
      const d = new Date(v);
      if (!isNaN(d)) return d;
    }
    return null;
  };

  const dateComparator = (valueA, valueB) => {
    const dateA = parseToDate(valueA);
    const dateB = parseToDate(valueB);
    if (dateA === null && dateB === null) return 0;
    if (dateA === null) return -1;
    if (dateB === null) return 1;
    return dateA.getTime() - dateB.getTime();
  };

  // Load forecast node colors for row styling
  useEffect(() => {
    const fetchNodeColors = async () => {
      try {
        const res = await api.bigQueryRunQueryOnCall({ options: { query: 'SELECT code, color FROM `hj-reporting.forecast.forecast_nodes`' } });
        const map = {};
        (res.data || []).forEach(r => {
          map[r.code] = r.color;
        });
        setNodeColorMap(map);
      } catch {
        message.error('Failed to load forecast node colors');
      }
    };
    fetchNodeColors();
  }, []);

  // Load item options for the select dropdown
  useEffect(() => {
    const fetchItemOptions = async () => {
      try {
        const result = await api.bigQueryRunQueryOnCall({
          options: {
            query: `
              SELECT 
                upc,
                externalid as sku,
                producttype,
                color,
                size,
                productspecification,
                lifestatus,
                internalid as netsuite_id
              FROM \`hj-reporting.items.items_netsuite\`
              WHERE 
                upc IS NOT NULL 
                AND upc != 'TRUE'
                AND LOWER(lifestatus) NOT IN ('obsolete', 'phasing out')
              ORDER BY upc
            `
          }
        });

        if (result.data && result.data.length > 0) {
          const options = result.data.map(item => ({
            value: item.upc,
            label: `${item.upc} - ${item.producttype} ${item.color} ${item.size}`.trim(),
            item: item
          }));
          setItemOptions(options);
        }
      } catch (error) {
        console.error('Error fetching item options:', error);
        message.error('Failed to load item options');
      }
    };
    fetchItemOptions();
  }, []);

  const onSelectionChanged = () => {
    if (gridRef.current?.api) {
      const selectedNodes = gridRef.current.api.getSelectedNodes();
      setSelectedRows(selectedNodes.length);
    }
  };

  const checkActiveFilters = () => {
    if (gridApiRef.current) {
      const filterModel = gridApiRef.current.getFilterModel();
      const hasFilters = Object.keys(filterModel).length > 0;
      setHasActiveFilters(hasFilters);
    }
  };

  const triggerRefreshOpenOrders = async (showSuccessMessage = true) => {
    if (taskProcessingState.isSelectiveUpdate) {
      console.log('Skipping manual refresh - selective update in progress');
      message.info('Data is currently being updated. Please wait...');
      return;
    }

    setOpenOrdersRefreshing(true);
    try {
      // Fetch all orders without any filters initially
      const startTime = Date.now();
      console.log('🔄 Refreshing orders to check for NetSuite confirmations...');
      await api.refreshOpenOrdersOnCall({ filters: {} });

      // The Firestore listener will automatically pick up changes and trigger updateRowsSelectively
      // This will detect NetSuite confirmations and complete waiting tasks

      if (showSuccessMessage) {
        message.success('Orders refreshed successfully');
      }
      console.log('✅ Orders refresh completed in', (Date.now() - startTime) / 1000, 'seconds');
    } catch (err) {
      console.error(err, err.message, err.stack);
      message.error('Failed to refresh orders: ' + err.message);
    } finally {
      setOpenOrdersRefreshing(false);
    }
  };

  // Expose refresh function globally for process queue access
  React.useEffect(() => {
    window.triggerRefreshOpenOrders = triggerRefreshOpenOrders;
    window.checkNetSuiteConfirmation = checkNetSuiteConfirmation;
    return () => {
      delete window.triggerRefreshOpenOrders;
      delete window.checkNetSuiteConfirmation;
    };
  }, [triggerRefreshOpenOrders, checkNetSuiteConfirmation]);
  const fetchOpenOrders = async () => {
    // Don't set loading state - we want to maintain table interactivity
    const q = query(collection(db, 'openOrders'), where('quantityopen', '>', 0), orderBy('canceldate', 'asc'));
    const unsubscribe = onSnapshot(q, (snapshot) => {
      const now = Date.now();
      const timeSinceLastUpdate = now - taskProcessingState.lastFirestoreUpdate;

      // Debounce rapid updates (less than 500ms apart) unless it's been more than 5 seconds
      if (timeSinceLastUpdate < 500 && timeSinceLastUpdate < 5000 && rowData.length > 0) {
        console.log('Debouncing Firestore update - too frequent');
        return;
      }

      updateTaskProcessingState({ lastFirestoreUpdate: now });
      const data = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));

      // Always use selective updates to maintain table functionality
      const isInitialLoad = rowData.length === 0;

      if (isInitialLoad) {
        console.log('Initial load of Firestore data:', data.length, 'rows');
        // Initial load - set all data but still use selective update mechanism
        setRowData(data);
        setOriginalRowData(data.map(r => ({ ...r })));
        setHasChanges(false);

        // Default bulkStrategy to netsuite's current strategy if uniform across rows
        if (data.length > 0) {
          const ids = data.map(r => r.allocationstrategyid);
          const uniqueIds = Array.from(new Set(ids));
          setBulkStrategy(uniqueIds.length === 1 ? uniqueIds[0] : null);
        }

        // No need to set loading false since we never set it to true
        gridApiRef.current?.hideOverlay();
      } else {
        // Always check for NetSuite confirmations first
        if (taskProcessingState.waitingForConfirmation.size > 0) {
          checkForNetSuiteConfirmations(data);
        }

        // ALWAYS perform selective updates to maintain table functionality
        // This ensures the table stays interactive and doesn't lose user state
        console.log('Firestore update detected, performing selective row updates to maintain table functionality');
        updateRowsSelectively(data);
      }
    });
    return () => unsubscribe();
  };

  // Selectively update only changed rows to maintain interactivity
  const updateRowsSelectively = (newData) => {
    console.log(`updateRowsSelectively called with ${newData.length} new rows, current data has ${rowData.length} rows`);

    // Set flag to indicate we're doing selective updates
    updateTaskProcessingState({ isSelectiveUpdate: true });

    setRowData(currentRowData => {
      const updatedRows = [];
      const rowsToRefresh = [];
      const netsuiteConfirmedRows = [];

      // Create a map for faster lookups
      const newDataMap = new Map();
      newData.forEach(row => {
        const key = `${row.transactionid}_${row.lineid}`;
        newDataMap.set(key, row);
      });

      console.log(`Processing ${currentRowData.length} current rows against ${newData.length} new rows`);

      // Update existing rows and track changes
      const updatedData = currentRowData.map(currentRow => {
        const key = `${currentRow.transactionid}_${currentRow.lineid}`;
        const newRow = newDataMap.get(key);

        if (newRow) {
          // Smart change detection - ignore timestamp and metadata changes
          const significantFieldsChanged = checkSignificantChanges(currentRow, newRow);

          if (significantFieldsChanged) {
            updatedRows.push(key);
            rowsToRefresh.push(currentRow.transactionid + '_' + currentRow.lineid);

            // Check if this was a NetSuite confirmation (row was in updating state)
            if (taskProcessingState.updatingRows.has(key)) {
              netsuiteConfirmedRows.push(key);
              console.log(`NetSuite confirmed changes for row: ${key}`, {
                old: getSignificantFields(currentRow),
                new: getSignificantFields(newRow)
              });
            }

            return newRow;
          }

          // Remove from map to track processed rows
          newDataMap.delete(key);
          return currentRow;
        }

        return currentRow;
      });

      // Add any new rows that weren't in the current data
      newDataMap.forEach(newRow => {
        // Check if this row already exists to prevent duplicates
        const existingRow = updatedData.find(row =>
          row.transactionid === newRow.transactionid && row.lineid === newRow.lineid
        );

        if (!existingRow) {
          updatedData.push(newRow);
          updatedRows.push(`${newRow.transactionid}_${newRow.lineid}`);
          console.log(`Added new row: ${newRow.transactionid}_${newRow.lineid}`);
        } else {
          console.log(`Skipped duplicate row: ${newRow.transactionid}_${newRow.lineid}`);
        }
      });

      console.log(`Selective update summary: ${updatedRows.length} rows updated, ${netsuiteConfirmedRows.length} NetSuite confirmations`);

      // Handle NetSuite confirmations
      if (netsuiteConfirmedRows.length > 0) {
        console.log(`🎉 NetSuite confirmed ${netsuiteConfirmedRows.length} rows:`, netsuiteConfirmedRows);

        // Clear updating state and show success for confirmed rows
        clearProcessingTimeouts(netsuiteConfirmedRows);

        // Complete tasks in process queue for confirmed rows with proper NetSuite sync status
        netsuiteConfirmedRows.forEach(async (rowKey) => {
          const taskDocId = taskProcessingState.taskDocuments.get(rowKey);
          if (taskDocId) {
            try {
              await updateTaskStatus(taskDocId, 'completed', 'NetSuite confirmation received', {
                progress: 100,
                completedAt: Date.now(),
                netsuiteSync: {
                  status: 'confirmed',
                  confirmedAt: Date.now(),
                  confirmedBy: 'data_refresh'
                }
              });
              console.log(`✅ Completed task ${taskDocId} for row ${rowKey} - NetSuite confirmation verified`);
            } catch (error) {
              console.error(`Error completing task ${taskDocId}:`, error);
            }
          }
        });

        setUpdatingRows(prev => {
          const newSet = new Set(prev);
          netsuiteConfirmedRows.forEach(key => newSet.delete(key));
          return newSet;
        });

        setSuccessRows(prev => new Set([...prev, ...netsuiteConfirmedRows]));

        // Clear success state after animation
        setTimeout(() => {
          setSuccessRows(prev => {
            const newSet = new Set(prev);
            netsuiteConfirmedRows.forEach(key => newSet.delete(key));
            return newSet;
          });
        }, 3000);

        message.success(`NetSuite confirmed ${netsuiteConfirmedRows.length} change${netsuiteConfirmedRows.length !== 1 ? 's' : ''}`);
      }

      // Update original row data for changed rows only
      if (updatedRows.length > 0) {
        setOriginalRowData(currentOriginal => {
          return currentOriginal.map(originalRow => {
            const key = `${originalRow.transactionid}_${originalRow.lineid}`;
            const newRow = newData.find(r =>
              r.transactionid === originalRow.transactionid && 
              r.lineid === originalRow.lineid
            );
            return newRow || originalRow;
          });
        });

        // Refresh only the changed rows in the grid to maintain interactivity
        setTimeout(() => {
          if (gridRef.current?.api && rowsToRefresh.length > 0) {
            const nodesToRefresh = [];
            gridRef.current.api.forEachNode(node => {
              if (node.data) {
                const nodeKey = node.data.transactionid + '_' + node.data.lineid;
                if (rowsToRefresh.includes(nodeKey)) {
                  nodesToRefresh.push(node);
                }
              }
            });

            if (nodesToRefresh.length > 0) {
              console.log(`Refreshing ${nodesToRefresh.length} changed rows in grid (maintaining interactivity)`);
              try {
                gridRef.current.api.refreshCells({
                  rowNodes: nodesToRefresh,
                  force: true
                });
              } catch (error) {
                console.warn('Error refreshing cells:', error);
                // Fallback: refresh the entire grid if selective refresh fails
                gridRef.current.api.refreshCells();
              }
            }
          }
        }, 100);
      }

      return updatedData;
    });

    // Reset flag after selective update is complete
    setTimeout(() => updateTaskProcessingState({ isSelectiveUpdate: false }), 200);
  };

  // Check if significant business fields have changed (ignore metadata/timestamps)
  const checkSignificantChanges = (oldRow, newRow) => {
    const significantFields = [
      'allocationstrategyid', 'startdate', 'canceldate', 'prepdate', 'isclosed',
      'quantityordered', 'quantitycommitted', 'quantitypicked', 'quantityshipped',
      'upc', 'itemdesc', 'rate', 'price', 'status'
    ];

    return significantFields.some(field => {
      const oldVal = oldRow[field];
      const newVal = newRow[field];

      // Handle dates specially
      if (field.includes('date')) {
        return normalizeDate(oldVal) !== normalizeDate(newVal);
      }

      return oldVal !== newVal;
    });
  };

  // Get significant fields for logging
  const getSignificantFields = (row) => {
    const significantFields = [
      'allocationstrategyid', 'startdate', 'canceldate', 'prepdate', 'isclosed'
    ];

    const result = {};
    significantFields.forEach(field => {
      result[field] = row[field];
    });
    return result;
  };

  // Normalize dates for comparison
  const normalizeDate = (dateValue) => {
    if (!dateValue) return null;
    if (typeof dateValue === 'string') return dateValue;
    if (dateValue.seconds) return new Date(dateValue.seconds * 1000).toISOString().split('T')[0];
    if (dateValue instanceof Date) return dateValue.toISOString().split('T')[0];
    return String(dateValue);
  };

  // Check for NetSuite confirmations when we skip full updates
  const checkForNetSuiteConfirmations = (newData) => {
    const confirmedRows = [];

    console.log(`Checking NetSuite confirmations for ${taskProcessingState.waitingForConfirmation.size} rows waiting for confirmation`);

    // Check each row waiting for confirmation to see if it's been confirmed by NetSuite
    taskProcessingState.waitingForConfirmation.forEach(rowKey => {
      const [transactionid, lineid] = rowKey.split('_');
      const newRow = newData.find(r =>
        r.transactionid === transactionid && r.lineid === lineid
      );
      const currentRow = rowData.find(r =>
        r.transactionid === transactionid && r.lineid === lineid
      );

      if (newRow && currentRow) {
        const hasChanges = checkSignificantChanges(currentRow, newRow);
        console.log(`Row ${rowKey}: hasChanges=${hasChanges}`, {
          current: getSignificantFields(currentRow),
          new: getSignificantFields(newRow)
        });

        if (hasChanges) {
          confirmedRows.push(rowKey);
          console.log(`✅ NetSuite CONFIRMED changes for row: ${rowKey}`);
        }
      } else {
        console.log(`⚠️ Could not find data for waiting row: ${rowKey}`, {
          newRowExists: !!newRow,
          currentRowExists: !!currentRow
        });
      }
    });

    if (confirmedRows.length > 0) {
      // Clear confirmation timeouts and resolve promises
      confirmedRows.forEach(async (rowKey) => {
        const confirmationTimeoutId = taskProcessingState.processingTimeouts.get(`${rowKey}_confirmation`);
        const resolveFunction = taskProcessingState.processingTimeouts.get(`${rowKey}_resolve`);
        const taskDocId = taskProcessingState.processingTimeouts.get(`${rowKey}_taskDocId`);

        if (confirmationTimeoutId) {
          clearTimeout(confirmationTimeoutId);
        }

        // Update Firestore task status to confirmed
        if (taskDocId) {
          await updateTaskStatus(taskDocId, 'confirmed', 'NetSuite confirmed changes successfully', {
            confirmedAt: serverTimestamp(),
            netsuiteData: newData.find(r => `${r.transactionid}_${r.lineid}` === rowKey)
          });
        }

        // Note: Process queue task will be updated via processTask completion

        // Resolve the promise to complete the task in the queue
        if (resolveFunction) {
          console.log(`Resolving task promise for ${rowKey} - NetSuite confirmed!`);
          resolveFunction({ success: true, rowKey, confirmed: true });
        }

        updateMap('processingTimeouts', `${rowKey}_confirmation`, undefined);
        updateMap('processingTimeouts', `${rowKey}_resolve`, undefined);
        updateMap('processingTimeouts', `${rowKey}_taskDocId`, undefined);
      });

      // Move from waiting to confirmed success
      setWaitingForConfirmation(prev => {
        const newSet = new Set(prev);
        confirmedRows.forEach(key => newSet.delete(key));
        return newSet;
      });

      setSuccessRows(prev => new Set([...prev, ...confirmedRows]));

      // Clear success state after animation
      setTimeout(() => {
        setSuccessRows(prev => {
          const newSet = new Set(prev);
          confirmedRows.forEach(key => newSet.delete(key));
          return newSet;
        });
      }, 5000); // Show success longer since it's the real confirmation

      message.success(`🎉 NetSuite CONFIRMED ${confirmedRows.length} change${confirmedRows.length !== 1 ? 's' : ''}!`);

      // Now update the confirmed rows with NetSuite data
      setRowData(currentRowData => {
        return currentRowData.map(row => {
          const rowKey = `${row.transactionid}_${row.lineid}`;
          if (confirmedRows.includes(rowKey)) {
            const newRow = newData.find(r =>
              r.transactionid === row.transactionid && r.lineid === row.lineid
            );
            return newRow || row;
          }
          return row;
        });
      });

      // Update original data too
      setOriginalRowData(currentOriginalData => {
        return currentOriginalData.map(row => {
          const rowKey = `${row.transactionid}_${row.lineid}`;
          if (confirmedRows.includes(rowKey)) {
            const newRow = newData.find(r =>
              r.transactionid === row.transactionid && r.lineid === row.lineid
            );
            return newRow || row;
          }
          return row;
        });
      });
    }
  };
  const fetchLastUpdated = async () => {
    const unsubscribe = onSnapshot(doc(db, 'lists', 'lastUpdates'), (doc) => {
      setLastUpdated(doc.data()?.openOrders || 'Never');
    });
    return () => unsubscribe();
  };
  const fetchOpenOrdersRefreshing = async () => {
    const unsubscribe = onSnapshot(doc(db, 'lists', 'lastUpdates'), (doc) => {
      setOpenOrdersRefreshing(doc.data()?.openOrdersRefreshing || false);
    });
    return () => unsubscribe();
  };

  useEffect(() => {
    // Fetch orders immediately when component mounts
    fetchLastUpdated();
    fetchOpenOrders();
    fetchOpenOrdersRefreshing();
  }, []);

  // Cleanup timeouts and monitors on unmount
  useEffect(() => {
    return () => {
      // Clear all processing timeouts when component unmounts
      taskProcessingState.processingTimeouts.forEach(timeoutId => clearTimeout(timeoutId));

      // Clear all confirmation monitors
      clearAllConfirmationMonitors();
    };
  }, [taskProcessingState.processingTimeouts]);

  // Separate effect for fetching views based on user
  useEffect(() => {
    if (userData?.id) {
      const unsubscribe = fetchViews();
      return unsubscribe;
    }
  }, [userData?.id]);

  // Listen to task updates from Firestore
  useEffect(() => {
    if (userData?.uid) {
      const unsubscribe = listenToTaskUpdates();
      return unsubscribe;
    }
  }, [userData?.uid]);

  // Auto-clear stuck processing states (failsafe)
  useEffect(() => {
    const interval = setInterval(() => {
      const now = Date.now();
      const stuckRows = [];

      // Check for rows that have been processing for more than 5 minutes
      taskProcessingState.processingTimeouts.forEach((timeoutId, rowKey) => {
        const timeoutData = timeoutId;
        if (typeof timeoutData === 'object' && timeoutData.startTime) {
          if (now - timeoutData.startTime > 300000) { // 5 minutes
            stuckRows.push(rowKey);
          }
        }
      });

      if (stuckRows.length > 0) {
        console.warn(`Auto-clearing stuck processing states for rows:`, stuckRows);
        stuckRows.forEach(rowKey => {
          setUpdatingRows(prev => {
            const newSet = new Set(prev);
            newSet.delete(rowKey);
            return newSet;
          });
          setWaitingForConfirmation(prev => {
            const newSet = new Set(prev);
            newSet.delete(rowKey);
            return newSet;
          });
          setErrorRows(prev => new Set([...prev, rowKey]));
          setRowErrors(prev => new Map(prev.set(rowKey, 'Processing timeout - operation may have completed')));
        });
      }
    }, 60000); // Check every minute

    return () => clearInterval(interval);
  }, [taskProcessingState.processingTimeouts]);

  // Load user's last used view when views are available
  useEffect(() => {
    if (savedViews.length > 0 && userData?.lastOrderAllocationView && !selectedViewId) {
      const lastView = savedViews.find(v => v.id === userData.lastOrderAllocationView);
      if (lastView) {
        setSelectedViewId(userData.lastOrderAllocationView);
        applyViewState(userData.lastOrderAllocationView);
      }
    }
  }, [savedViews, userData?.lastOrderAllocationView, selectedViewId]);

  const onBulkApply = () => {
    const selectedNodes = gridRef.current.api.getSelectedNodes();
    if (selectedNodes.length === 0) {
      message.error('Please select at least one row');
      return;
    }

    let hasChanges = false;

    // Apply allocation strategy
    if (bulkStrategy) {
      selectedNodes.forEach(node => {
        if (node.data?.quantitypicked >= node.data?.quantityordered) {
          message.warning('Cannot change allocation strategy for a line that has been fully picked');
          return true;
        }
        node.setDataValue('allocationstrategyid', bulkStrategy);
      });
      hasChanges = true;
    }

    // Apply start date
    if (bulkStartDate) {
      const formattedStartDate = bulkStartDate.format('MM/DD/YYYY');
      selectedNodes.forEach(node => {
        node.setDataValue('startdate', formattedStartDate);
      });
      hasChanges = true;
    }

    // Apply cancel date
    if (bulkCancelDate) {
      const formattedCancelDate = bulkCancelDate.format('MM/DD/YYYY');
      selectedNodes.forEach(node => {
        node.setDataValue('canceldate', formattedCancelDate);
      });
      hasChanges = true;
    }

    // Apply prep date
    if (bulkPrepDate) {
      const formattedPrepDate = bulkPrepDate.format('MM/DD/YYYY');
      selectedNodes.forEach(node => {
        node.setDataValue('prepdate', formattedPrepDate);
      });
      hasChanges = true;
    }
    // Apply is closed
    if (bulkIsClosed) {
      selectedNodes.forEach(node => {
        if (node.data?.quantitypicked >= node.data?.quantityordered) {
          message.warning('Cannot change is closed for a line that has been fully picked');
          return true;
        }
        node.setDataValue('isclosed', bulkIsClosed);
      });
      hasChanges = true;
    }

    // Apply item change
    if (selectedNewItem) {
      selectedNodes.forEach(node => {
        if (node.data?.quantitypicked > 0) {
          message.warning('Cannot change item for a line that has been picked');
          return true;
        }
        node.setDataValue('upc', selectedNewItem.upc);
        node.setDataValue('itemdesc', `${selectedNewItem.producttype} ${selectedNewItem.color} ${selectedNewItem.size}`.trim());
        // Store the NetSuite ID for the save operation
        node.setDataValue('netsuite_id', selectedNewItem.netsuite_id);
        node.setDataValue('rate', node.data?.rate);
        node.setDataValue('price', { id: -1 });
      });
      hasChanges = true;
    }

    if (hasChanges) {
      setHasChanges(true);
      message.success('Bulk changes applied successfully');
      setBulkActionsVisible(false); // Hide the menu after applying changes
      // Reset form
      setBulkStrategy(null);
      setBulkStartDate(null);
      setBulkCancelDate(null);
      setBulkPrepDate(null);
      setSelectedNewItem(null);
    } else {
      message.warning('Please select at least one field to apply');
    }
  };

  const clearAllFilters = () => {
    if (gridApiRef.current) {
      gridApiRef.current.setFilterModel(null);
      setHasActiveFilters(false);
      message.success('All filters cleared');
    }
  };

  // Fetch saved views - user's private views + all public views
  const fetchViews = () => {
    if (!userData?.id) {
      setSavedViews([]);
      return () => {};
    }
    
    // Query to get user's private views + all public views
    const q = query(
      collection(db, 'orderAllocationViews'),
      or(
        where('createdBy', '==', userData.id), // User's own views (private)
        where('isPublic', '==', true) // All public views
      )
    );
    
    const unsub = onSnapshot(q, (snap) => {
      setSavedViews(snap.docs.map(doc => ({ id: doc.id, ...doc.data() })));
    });
    return unsub;
  };

  const handleSaveView = async () => {
    if (!userData?.id) {
      message.error('You must be logged in to save views');
      return;
    }

    const name = prompt('Enter a name for the view');
    if (!name || !name.trim()) return;
    
    // Sanitize the name to prevent XSS
    const sanitizedName = name.trim().substring(0, 100);
    
    try {
      if (!gridRef.current?.api) {
        message.error('Grid not ready for saving');
        return;
      }

      const api = gridRef.current.api;
      
      // Capture comprehensive grid state
      let gridState = {};
      try {
        gridState = {
          // Use getState() as primary method, but add fallbacks
          fullState: api.getState(),
          // Also capture individual components as backup
          columnState: api.getColumnState ? api.getColumnState() : [],
          columnGroupState: api.getColumnGroupState ? api.getColumnGroupState() : [],
          filterModel: api.getFilterModel ? api.getFilterModel() : {},
          sortModel: api.getSortModel ? api.getSortModel() : [],
          rowGroupCols: api.getRowGroupColumns ? api.getRowGroupColumns().map(c => c.getColId()) : [],
          pivotCols: api.getPivotColumns ? api.getPivotColumns().map(c => c.getColId()) : [],
          valueCols: api.getValueColumns ? api.getValueColumns().map(c => c.getColId()) : [],
          pivotMode: api.isPivotMode ? api.isPivotMode() : false
        };
      } catch (gridError) {
        console.warn('Some grid state could not be captured:', gridError);
        // Continue with partial state
        gridState = { fullState: api.getState() };
      }

      const newDoc = await addDoc(collection(db, 'orderAllocationViews'), {
        name: sanitizedName,
        gridState: JSON.parse(JSON.stringify(gridState)),
        createdBy: userData?.id,
        isPublic: false,
        createdByEmail: userData?.email,
        timestamp: Date.now()
      });
      
      // Update the user's preferred view
      await setDoc(doc(db, 'users', userData.id), {
        lastOrderAllocationView: newDoc.id,
      }, { merge: true });
      
      setSelectedViewId(newDoc.id);
      message.success('View saved successfully');
    } catch (error) {
      console.error('Error saving view:', error);
      message.error('Failed to save view');
    }
  };

  const applyViewState = async (viewId) => {
    if (!gridRef?.current?.api) {
      console.warn('Grid API not ready');
      return;
    }

    const api = gridRef.current.api;
    const selectedView = savedViews.find(view => view.id === viewId);
    
    if (!selectedView || !selectedView.gridState) {
      console.warn('Selected view or grid state not found');
      // Reset to default state
      api.setState({});
      return;
    }

    try {
      const gridState = selectedView.gridState;
      
      // Try to apply full state first (newer saves)
      if (gridState.fullState) {
        api.setState(gridState.fullState);
        
        // Add a small delay to ensure filters are applied
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // If filters weren't applied by fullState, try applying them individually as fallback
        if (gridState.filterModel && Object.keys(gridState.filterModel).length > 0) {
          const currentFilters = api.getFilterModel() || {};
          const hasExpectedFilters = Object.keys(gridState.filterModel).every(key => 
            currentFilters[key] && JSON.stringify(currentFilters[key]) === JSON.stringify(gridState.filterModel[key])
          );
          
          if (!hasExpectedFilters) {
            await new Promise(resolve => setTimeout(resolve, 50));
            api.setFilterModel(gridState.filterModel);
            await new Promise(resolve => setTimeout(resolve, 50));
          }
        }
      } else {
        // Fallback to individual components (older saves or partial state)
        // Apply column state first
        if (gridState.columnState) {
          api.applyColumnState({
            state: gridState.columnState,
            applyOrder: true,
            applySize: true,
            applyVisible: true,
            applySort: false // Apply sorting separately
          });
        }

        // Apply column group state
        if (gridState.columnGroupState) {
          api.setColumnGroupState(gridState.columnGroupState);
        }

        // Apply sorting
        if (gridState.sortModel) {
          api.setSortModel(gridState.sortModel);
        }

        // Apply row grouping
        if (gridState.rowGroupCols && gridState.rowGroupCols.length > 0) {
          api.setRowGroupColumns(gridState.rowGroupCols);
        }

        // Apply pivot columns
        if (gridState.pivotCols && gridState.pivotCols.length > 0) {
          api.setPivotColumns(gridState.pivotCols);
        }

        // Apply value columns
        if (gridState.valueCols && gridState.valueCols.length > 0) {
          api.setValueColumns(gridState.valueCols);
        }

        // Apply pivot mode
        if (gridState.pivotMode !== undefined) {
          api.setPivotMode(gridState.pivotMode);
        }

        // Apply filters LAST with a delay to ensure they stick
        if (gridState.filterModel) {
          await new Promise(resolve => setTimeout(resolve, 100));
          api.setFilterModel(gridState.filterModel);
          // Additional delay to ensure filters are fully applied
          await new Promise(resolve => setTimeout(resolve, 50));
        }
      }
    } catch (error) {
      console.error('Error applying view state:', error);
      message.error('Failed to apply saved view');
      // Reset to default state on error
      api.setState({});
    }
  };

  const handleDeleteView = async (id) => {
    const view = savedViews.find(v => v.id === id);
    if (!view) return;

    if (view.createdBy !== userData?.id) {
      message.error('You can only delete your own views');
      return;
    }

    await deleteDoc(doc(db, 'orderAllocationViews', id));
    setSavedViews(views => views.filter(v => v.id !== id));

    if (userData?.lastOrderAllocationView === id) {
      if (gridRef.current && gridRef.current.api) {
        gridRef.current.api.resetColumnState();
        gridRef.current.api.resetColumnGroupState();
        gridRef.current.api.setFilterModel(null);
        gridRef.current.api.setRowGroupColumns([]);
      }
    }

    // If this was the user's last used view, clear the reference
    if (userData?.id && userData.lastOrderAllocationView === id) {
      setDoc(doc(db, 'users', userData.id), {
        lastOrderAllocationView: null
      }, { merge: true });
    }
    if (selectedViewId === id) {
      setSelectedViewId(null);
    }

    message.success('View deleted successfully');
  };

  const handleToggleViewVisibility = async (id) => {
    const view = savedViews.find(v => v.id === id);
    if (!view || view.createdBy !== userData?.id) {
      message.error('You can only modify your own views');
      return;
    }

    try {
      await setDoc(doc(db, 'orderAllocationViews', id), {
        isPublic: !view.isPublic,
        timestamp: Date.now()
      }, { merge: true });

      await fetchViews();
      message.success(`View is now ${!view.isPublic ? 'public' : 'private'}`);
      // If the toggled view is currently selected, reapply it so grid state updates
      if (userData?.lastOrderAllocationView === id && gridApiRef.current) {
        const updated = (await getDocs(query(collection(db, 'orderAllocationViews'), where('__name__', '==', id)))).docs[0];
        if (updated) handleViewChange(id);
      }
    } catch (error) {
      console.error('Error toggling view visibility:', error);
      message.error('Failed to update view visibility');
    }
  };

  const bulkActionContent = (
    <Space direction="vertical" style={{ width: 350 }}>
      <Text strong>Bulk Actions</Text>

      <Text>Allocation Strategy:</Text>
      <Select
        style={{ width: '100%' }}
        options={Object.entries(ORDER_ALLOCATION_STRATEGIES).map(([key, value]) => ({ label: value, value: key }))}
        value={bulkStrategy}
        onChange={setBulkStrategy}
        placeholder="Select strategy"
        allowClear
      />

      <Text>Start Date:</Text>
      <DatePicker
        style={{ width: '100%' }}
        value={bulkStartDate}
        onChange={setBulkStartDate}
        format="MM/DD/YYYY"
        placeholder="Select start date"
      />
      <Text>Prep Date:</Text>
      <DatePicker
        style={{ width: '100%' }}
        value={bulkPrepDate}
        onChange={setBulkPrepDate}
        format="MM/DD/YYYY"
        placeholder="Select prep date"
      />

      <Text>Cancel Date:</Text>
      <DatePicker
        style={{ width: '100%' }}
        value={bulkCancelDate}
        onChange={setBulkCancelDate}
        format="MM/DD/YYYY"
        placeholder="Select cancel date"
      />

      <Text>Is Closed:</Text>
      <Checkbox
        checked={bulkIsClosed}
        onChange={(e) => {
          console.log('checked', e.target.checked);
          setBulkIsClosed(e.target.checked);
        }}
      />


      <Text>Change Item:</Text>
      <Select
        style={{ width: '100%' }}
        showSearch
        filterOption={(input, option) => {
          if (!input) return true;
          const searchText = input.toLowerCase();
          const optionText = option?.label?.toLowerCase() || '';
          const optionValue = option?.value?.toLowerCase() || '';

          // Split search terms and check if all terms are found in the option text
          const searchTerms = searchText.split(/\s+/).filter(term => term.length > 0);
          return searchTerms.every(term =>
            optionText.includes(term) || optionValue.includes(term)
          );
        }}
        placeholder="Search by UPC, SKU, or description..."
        value={selectedNewItem?.upc}
        onChange={(value) => {
          const selectedOption = itemOptions.find(option => option.value === value);
          setSelectedNewItem(selectedOption ? selectedOption.item : null);
        }}
        allowClear
        options={itemOptions}
      />

      {selectedNewItem && (
        <div style={{ padding: 8, backgroundColor: '#f6ffed', border: '1px solid #b7eb8f', borderRadius: 4 }}>
          <Text strong>Selected Item:</Text>
          <br />
          <Text>{selectedNewItem.upc} - {selectedNewItem.producttype} {selectedNewItem.color} {selectedNewItem.size}</Text>
          <br />
          <Text type="secondary">SKU: {selectedNewItem.sku} | {selectedNewItem.productspecification}</Text>
        </div>
      )}

      <Button
        type="primary"
        onClick={onBulkApply}
        style={{ width: '100%', marginTop: 8 }}
      >
        Apply to Selected Rows
      </Button>
    </Space>
  );

  const columnDefs = [
    {
      headerName: 'Status',
      field: 'status',
      enableRowGroup: true,
      filter: true,
      sortable: true,
      width: 200,
      // cellStyle: ({ value }) => ({ color: SO_STATUS_COLORS[value] || 'inherit' }),
      cellRenderer: params => {
        if (!params.data) return null;

        const rowKey = `${params.data.transactionid}_${params.data.lineid}`;
        const isUpdating = taskProcessingState.updatingRows.has(rowKey);
        const hasError = taskProcessingState.errorRows.has(rowKey);
        const hasSuccess = taskProcessingState.successRows.has(rowKey);
        const isWaitingForConfirmation = taskProcessingState.waitingForConfirmation.has(rowKey);
        const errorMessage = taskProcessingState.rowErrors.get(rowKey);

        // If row is in any processing state, replace the status content entirely
        if (isUpdating || ['queued', 'processing'].includes(params.data?.syncStatus)) {
          return (
            <div className="processing-status updating" style={{ height: '95%', display: 'flex', alignItems: 'center', gap: '8px' }}>
              <Spin size="small" />
              <span>Saving to NetSuite...</span>
            </div>
          );
        } else if (isWaitingForConfirmation) {
          return (
            <div className="processing-status updating" style={{ height: '95%', display: 'flex', alignItems: 'center', gap: '8px' }}>
              <Spin size="small" />
              <span>Waiting for NetSuite confirmation...</span>
            </div>
          );
        } else if (hasError) {
          return (
            <div className="processing-status error" style={{ height: '95%', display: 'flex', alignItems: 'center', gap: '8px', justifyContent: 'space-between' }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <span>⚠</span>
                <span>Failed</span>
              </div>
              <button
                className="retry-button"
                onClick={(e) => {
                  e.stopPropagation();
                  retryRow(params.data);
                }} 
                title={errorMessage || 'Click to retry this row'}
              >
                Retry
              </button>
            </div>
          );
        } else if (hasSuccess) {
          return (
            <div className="processing-status success" style={{ height: '95%', display: 'flex', alignItems: 'center', gap: '8px' }}>
              <span>🎉</span>
              <span>NetSuite Confirmed!</span>
            </div>
          );
        }

        // Default: show original status tag
        return (
          <div style={{ height: '95%', display: 'flex', alignItems: 'center' }}>
            <Tag color={SO_STATUS_COLORS[params.value] || 'inherit'}>{params.value}</Tag>
          </div>
        );
      },
    },
    {
      headerName: 'Date',
      field: 'orderdate',
      sortable: true,
      filter: 'agDateColumnFilter',
      filterParams: {
        filterOptions: ['equals', 'lessThan', 'greaterThan', 'inRange'],
        defaultOption: 'equals',
        // comparator used by the date filter: compare filter date (JS Date at midnight) to cell value
        comparator: (filterLocalDateAtMidnight, cellValue) => {
          const cellDate = parseToDate(cellValue);
          if (!cellDate) return -1;
          // strip time from cellDate
          const cellDateNoTime = new Date(cellDate.getFullYear(), cellDate.getMonth(), cellDate.getDate());
          if (cellDateNoTime < filterLocalDateAtMidnight) return -1;
          if (cellDateNoTime > filterLocalDateAtMidnight) return 1;
          return 0;
        }
      },
      comparator: dateComparator,
      valueFormatter: params => {
        const d = parseToDate(params.value);
        if (!d) return '';
        return d.toISOString().split('T')[0];
      },
    },
    {
      headerName: 'SO #',
      field: 'docnumber',
      sortable: true,
      filter: 'agTextColumnFilter',
      filterParams: {
        filterOptions: [
          'contains',
          'equals',
          'startsWith',
          {
            displayKey: 'multiValue',
            displayName: 'Multiple SO Numbers',
            predicate: (filterValues, cellValue) => {
              if (!filterValues || filterValues.length === 0) return true;
              if (!cellValue) return false;

              // Split the filter value by common separators
              const terms = filterValues[0].split(/[\s,]+/).map(t => t.trim()).filter(Boolean);
              return terms.some(term =>
                cellValue.toString().toLowerCase().includes(term.toLowerCase())
              );
            },
            numberOfInputs: 1
          }
        ],
        defaultOption: 'multiValue'
      },
      cellRenderer: params => (
        <a
          href={'https://6810379.app.netsuite.com/app/accounting/transactions/salesord.nl?id=' + params.data?.transactionid + '&whence='}
          target='_blank'
          rel='noopener noreferrer'
        >
          <span style={{ textDecoration: 'underline' }}>{params.value}</span>
        </a>
      )
    },
    {
      headerName: 'Customer',
      field: 'customername',
      enableRowGroup: true,
      sortable: true,
      filter: 'agSetColumnFilter',
      filterParams: {
        filterOptions: ['contains', 'equals', 'startsWith'],
        defaultOption: 'contains'
      },
    },
    {
      headerName: 'PO #',
      field: 'ponumber',
      sortable: true,
      filter: 'agTextColumnFilter',
      filterParams: {
        filterOptions: [
          'contains',
          'equals',
          'startsWith',
          {
            displayKey: 'multiValue',
            displayName: 'Multiple PO Numbers',
            predicate: (filterValues, cellValue) => {
              if (!filterValues || filterValues.length === 0) return true;
              if (!cellValue) return false;

              // Split the filter value by common separators
              const terms = filterValues[0].split(/[\s,]+/).map(t => t.trim()).filter(Boolean);
              return terms.some(term =>
                cellValue.toString().toLowerCase().includes(term.toLowerCase())
              );
            },
            numberOfInputs: 1
          }
        ],
        defaultOption: 'multiValue'
      },
    },
    {
      headerName: 'Line #',
      field: 'lineid',
      hide: true,
      filter: 'agNumberColumnFilter',
      filterParams: {
        filterOptions: ['equals', 'lessThan', 'greaterThan', 'inRange'],
        defaultOption: 'equals'
      },
      cellStyle: ({ value, data }) => {
        if (data?.quantitypicked > 0) {
          return { color: 'gray', fontStyle: 'italic' };
        }
        return null;
      },
    },
    {
      headerName: 'UPC',
      field: 'upc',
      enableRowGroup: true,
      hide: true,
      sortable: true,
      filter: 'agTextColumnFilter',
      filterParams: {
        filterOptions: [
          'contains',
          'equals',
          'startsWith',
          {
            displayKey: 'multiValue',
            displayName: 'Multiple UPCs',
            predicate: (filterValues, cellValue) => {
              if (!filterValues || filterValues.length === 0) return true;
              if (!cellValue) return false;

              // Split the filter value by common separators
              const terms = filterValues[0].split(/[\s,]+/).map(t => t.trim()).filter(Boolean);
              return terms.some(term =>
                cellValue.toString().toLowerCase().includes(term.toLowerCase())
              );
            },
            numberOfInputs: 1
          }
        ],
        defaultOption: 'multiValue'
      },
      cellStyle: ({ value, data }) => {
        const orig = originalRowData.find(r => r.id === data?.id && r.lineid === data?.lineid);
        if (orig && orig.upc !== value) {
          return { color: 'blue', fontWeight: 'bold' };
        }
        return null;
      },
    },
    {
      headerName: 'Life Status',
      field: 'lifestatus',
      enableRowGroup: true,
      hide: true,
      sortable: true,
      filter: 'agSetColumnFilter',
    },
    {
      headerName: 'Color',
      field: 'color',
      enableRowGroup: true,
      hide: true,
      sortable: true,
      filter: 'agSetColumnFilter',
    },
    {
      headerName: 'Size',
      field: 'size',
      enableRowGroup: true,
      hide: true,
      sortable: true,
      filter: 'agSetColumnFilter',
    },
    {
      headerName: 'Division',
      field: 'division',
      enableRowGroup: true,
      hide: true,
      sortable: true,
      filter: 'agSetColumnFilter',
    },
    {
      headerName: 'Category',
      field: 'category',
      enableRowGroup: true,
      hide: true,
      sortable: true,
      filter: 'agSetColumnFilter',
    },
    {
      headerName: 'Form',
      field: 'form',
      enableRowGroup: true,
      hide: true,
      sortable: true,
      filter: 'agSetColumnFilter',
    }, {
      headerName: 'Family',
      field: 'family',
      enableRowGroup: true,
      hide: true,
      sortable: true,
      filter: 'agSetColumnFilter',
    },
    {
      headerName: 'Specification',
      field: 'specification',
      enableRowGroup: true,
      hide: true,
      sortable: true,
      filter: 'agSetColumnFilter',
    },
    {
      headerName: 'Item Desc',
      field: 'itemdesc',
      hide: false,
      sortable: true,
      filter: 'agTextColumnFilter',
      filterParams: {
        filterOptions: ['contains', 'equals', 'startsWith'],
        defaultOption: 'contains'
      },
      cellStyle: ({ value, data }) => {
        const orig = originalRowData.find(r => r.id === data?.id && r.lineid === data?.lineid);
        if (orig && orig.itemdesc !== value) {
          return { color: 'blue', fontWeight: 'bold' };
        }
        return null;
      },
    },
    {
      headerName: 'Org Cancel',
      field: 'orgcancel',
      sortable: true,
      comparator: dateComparator,
      filter: 'agDateColumnFilter',
      filterParams: {
        filterOptions: ['equals', 'lessThan', 'greaterThan', 'inRange'],
        defaultOption: 'equals',
        comparator: (filterLocalDateAtMidnight, cellValue) => {
          const cellDate = parseToDate(cellValue);
          if (!cellDate) return -1;
          const cellDateNoTime = new Date(cellDate.getFullYear(), cellDate.getMonth(), cellDate.getDate());
          if (cellDateNoTime < filterLocalDateAtMidnight) return -1;
          if (cellDateNoTime > filterLocalDateAtMidnight) return 1;
          return 0;
        }
      },
      valueFormatter: params => {
        const d = parseToDate(params.value);
        if (!d) return '';
        return d.toISOString().split('T')[0];
      },
      cellStyle: ({ value }) => {
        const d = parseToDate(value);
        if (!d) return null;
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        if (d < today) return { color: 'darkred', fontWeight: 'bold' };
      }
    },
    {
      headerName: 'Ordered',
      field: 'quantityordered',
      type: 'numericColumn',
      sortable: true,
      filter: 'agNumberColumnFilter',
      filterParams: {
        filterOptions: ['equals', 'lessThan', 'greaterThan', 'inRange'],
        defaultOption: 'equals'
      },
      aggFunc: 'sum',
      valueGetter: params => parseFloat(params.data?.quantityordered) || 0,
      cellStyle: ({ value, data }) => {
        return (value === 0 ? { color: '#d4b106', fontWeight: 'bold' } : null);
      },
    },
    {
      headerName: 'Committed',
      field: 'quantitycommitted',
      type: 'numericColumn',
      sortable: true,
      filter: 'agNumberColumnFilter',
      filterParams: {
        filterOptions: ['equals', 'lessThan', 'greaterThan', 'inRange'],
        defaultOption: 'equals'
      },
      aggFunc: 'sum',
      valueGetter: params => parseFloat(params.data?.quantitycommitted) || 0,
      cellStyle: ({ value, data }) => {
        return (value === 0 ? { color: '#d4b106', fontWeight: 'bold' } : null);
      },
    },
    {
      headerName: 'Picked',
      field: 'quantitypicked',
      type: 'numericColumn',
      sortable: true,
      filter: 'agNumberColumnFilter',
      filterParams: {
        filterOptions: ['equals', 'lessThan', 'greaterThan', 'inRange'],
        defaultOption: 'equals'
      },
      aggFunc: 'sum',
      valueGetter: params => parseFloat(params.data?.quantitypicked) || 0,
      cellStyle: ({ value, data }) => {
        return (value >= 0 ? { color: '#d4b106', fontWeight: 'bold' } : null);
      },
    },
    {
      headerName: 'Shipped',
      field: 'quantityshipped',
      sortable: true,
      hide: true,
      filter: 'agNumberColumnFilter',
      filterParams: {
        filterOptions: ['equals', 'lessThan', 'greaterThan', 'inRange'],
        defaultOption: 'equals'
      },
      aggFunc: 'sum',
      valueGetter: params => parseFloat(params.data?.quantityshipped) || 0,
      cellStyle: ({ value }) => (value === 0 ? { color: '#d4b106', fontWeight: 'bold' } : null),
    },
    {
      headerName: 'Billed',
      field: 'quantitybilled',
      sortable: true,
      filter: 'agNumberColumnFilter',
      filterParams: {
        filterOptions: ['equals', 'lessThan', 'greaterThan', 'inRange'],
        defaultOption: 'equals'
      },
      hide: true,
      aggFunc: 'sum',
      valueGetter: params => parseFloat(params.data?.quantitybilled) || 0,
      cellStyle: ({ value }) => (value === 0 ? { color: '#d4b106', fontWeight: 'bold' } : null),
    },
    {
      headerName: 'Start',
      field: 'startdate',
      editable: true,
      sortable: true,
      filter: 'agDateColumnFilter',
      filterParams: {
        filterOptions: ['equals', 'lessThan', 'greaterThan', 'inRange'],
        defaultOption: 'equals'
      },
      cellEditor: 'agDateCellEditor',
      valueFormatter: params => {
        const d = parseToDate(params.value);
        if (!d) return '';
        return d.toISOString().split('T')[0];
      },
      valueParser: params => {
        const v = params.newValue;
        const d = new Date(v);
        if (!isNaN(d)) {
          const mm = (d.getMonth() + 1).toString().padStart(2, '0');
          const dd = d.getDate().toString().padStart(2, '0');
          const yyyy = d.getFullYear();
          return `${mm}/${dd}/${yyyy}`;
        }
        return v;
      },
      sortable: true,
      comparator: dateComparator,
      filterParams: {
        comparator: (filterLocalDateAtMidnight, cellValue) => {
          const cellDate = parseToDate(cellValue);
          if (!cellDate) return -1;
          const cellDateNoTime = new Date(cellDate.getFullYear(), cellDate.getMonth(), cellDate.getDate());
          if (cellDateNoTime < filterLocalDateAtMidnight) return -1;
          if (cellDateNoTime > filterLocalDateAtMidnight) return 1;
          return 0;
        }
      },
    },
    {
      headerName: 'Prep',
      field: 'prepdate',
      sortable: true,
      filter: 'agDateColumnFilter',
      editable: true,
      filterParams: {
        filterOptions: ['equals', 'lessThan', 'greaterThan', 'inRange'],
        defaultOption: 'equals'
      },
      cellEditor: 'agDateCellEditor',
      valueFormatter: params => {
        const d = parseToDate(params.value);
        if (!d) return '';
        return d.toISOString().split('T')[0];
      },
      valueParser: params => {
        const v = params.newValue;
        const d = new Date(v);
        if (!isNaN(d)) {
          const mm = (d.getMonth() + 1).toString().padStart(2, '0');
          const dd = d.getDate().toString().padStart(2, '0');
          const yyyy = d.getFullYear();
          return `${mm}/${dd}/${yyyy}`;
        }
        return v;
      },
      sortable: true,
      comparator: dateComparator,
      filterParams: {
        comparator: (filterLocalDateAtMidnight, cellValue) => {
          const cellDate = parseToDate(cellValue);
          if (!cellDate) return -1;
          const cellDateNoTime = new Date(cellDate.getFullYear(), cellDate.getMonth(), cellDate.getDate());
          if (cellDateNoTime < filterLocalDateAtMidnight) return -1;
          if (cellDateNoTime > filterLocalDateAtMidnight) return 1;
          return 0;
        }
      },
    },
    {
      headerName: 'Cancel',
      field: 'canceldate',
      editable: true,
      enableRowGroup: true,
      sortable: true,
      filter: 'agDateColumnFilter',
      filterParams: {
        filterOptions: ['equals', 'lessThan', 'greaterThan', 'inRange'],
        defaultOption: 'equals'
      },
      cellEditor: 'agDateCellEditor',
      valueFormatter: params => {
        const d = parseToDate(params.value);
        if (!d) return '';
        return d.toISOString().split('T')[0];
      },
      valueParser: params => {
        const v = params.newValue;
        const d = new Date(v);
        if (!isNaN(d)) {
          const mm = (d.getMonth() + 1).toString().padStart(2, '0');
          const dd = d.getDate().toString().padStart(2, '0');
          const yyyy = d.getFullYear();
          return `${mm}/${dd}/${yyyy}`;
        }
        return v;
      },
      sortable: true,
      comparator: dateComparator,
      filterParams: {
        comparator: (filterLocalDateAtMidnight, cellValue) => {
          const cellDate = parseToDate(cellValue);
          if (!cellDate) return -1;
          const cellDateNoTime = new Date(cellDate.getFullYear(), cellDate.getMonth(), cellDate.getDate());
          if (cellDateNoTime < filterLocalDateAtMidnight) return -1;
          if (cellDateNoTime > filterLocalDateAtMidnight) return 1;
          return 0;
        }
      },
      cellStyle: ({ value, data }) => {
        const d = parseToDate(value);
        if (!d) return null;
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        if (d < today) return { color: '#a8071a', fontWeight: 'bold' };
        const in7 = new Date(today);
        in7.setDate(in7.getDate() + 7);
        if (d > today && d <= in7) return { color: '#fa8c16', fontWeight: 'bold' };
        const orig = originalRowData.find(r => r.id === data?.id && r.lineid === data?.lineid);
        const origDate = parseToDate(orig?.canceldate);
        if (origDate && origDate.getTime() !== d.getTime()) return { color: 'blue', fontWeight: 'bold' };
        return null;
      },
    },
    {
      headerName: 'Exp Ship',
      field: 'expectedshipdate',
      sortable: true,
      hide: true,
      filter: 'agDateColumnFilter',
      filterParams: {
        filterOptions: ['equals', 'lessThan', 'greaterThan', 'inRange'],
        defaultOption: 'equals',
        comparator: (filterLocalDateAtMidnight, cellValue) => {
          const cellDate = parseToDate(cellValue);
          if (!cellDate) return -1;
          const cellDateNoTime = new Date(cellDate.getFullYear(), cellDate.getMonth(), cellDate.getDate());
          if (cellDateNoTime < filterLocalDateAtMidnight) return -1;
          if (cellDateNoTime > filterLocalDateAtMidnight) return 1;
          return 0;
        }
      },
      valueFormatter: params => {
        const d = parseToDate(params.value);
        if (!d) return '';
        return d.toISOString().split('T')[0];
      },
      cellStyle: ({ value }) => {
        const d = parseToDate(value);
        if (!d) return null;
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        if (d < today) return { color: 'darkred', fontWeight: 'bold' };
        return null;
      }
    },
    {
      headerName: 'Allocation Strategy',
      field: 'allocationstrategyid',
      enableRowGroup: true,
      editable: (params) => {
        if (params.data?.quantitypicked >= params.data?.quantityordered) {
          return false;
        }
        return true;
      },
      cellEditor: 'agSelectCellEditor',
      sortable: true,
      filter: 'agSetColumnFilter',
      filterParams: {
        valueFormatter: params => ORDER_ALLOCATION_STRATEGIES[params.value] || params.value
      },
      cellEditorParams: { values: Object.keys(ORDER_ALLOCATION_STRATEGIES) },
      valueFormatter: params => ORDER_ALLOCATION_STRATEGIES[params.value] || '',
      valueParser: params => Object.keys(ORDER_ALLOCATION_STRATEGIES).find(key => ORDER_ALLOCATION_STRATEGIES[key] === params.newValue) || params.oldValue,
      cellStyle: ({ value, data }) => {
        if (data?.quantitypicked >= data?.quantityordered) {
          return { color: 'gray', fontStyle: 'italic' };
        }
        const orig = originalRowData.find(r => r.id === data?.id && r.lineid === data?.lineid);
        if (orig && orig.allocationstrategyid !== value) {
          return { color: 'blue', fontWeight: 'bold' };
        }
        return null;
      },
    },
    {
      headerName: 'Is Closed',
      field: 'isclosed',
      editable: (params) => {
        if (params.data?.quantitypicked >= params.data?.quantityordered) {
          return false;
        }
        return true;
      },
      cellEditor: 'agCheckboxCellEditor',
    },
    {
      headerName: 'NetSuite ID',
      field: 'netsuite_id',
      hide: true,
      sortable: true,
      filter: 'agNumberColumnFilter',
      filterParams: {
        filterOptions: ['equals', 'lessThan', 'greaterThan', 'inRange'],
        defaultOption: 'equals'
      }
    },
  ];

  const handleSave = () => {
    const changes = [];
    console.log('Starting handleSave - checking for changes...');

    gridRef.current.api.forEachNode(node => {
      const data = node.data;
      if (!data) return; // Skip group rows or nodes without data
      const orig = originalRowData.find(r => r.transactionid === data.transactionid && r.lineid === data.lineid);
      if (!orig) {
        console.warn('Could not find original data for row:', data.transactionid, data.lineid);
        return;
      }
      const change = {
        id: data.transactionid,
        lineid: data.lineid,
        linenumber: data.linenumber,
        docnumber: data.docnumber,
        ponumber: data.ponumber,
        customername: data.customername,
        quantityordered: data.quantityordered,
        quantitycommitted: data.quantitycommitted,
        upc: data.upc,
        itemdesc: data.itemdesc
      };
      let hasChange = false;

      // Check allocation strategy changes
      if (orig.allocationstrategyid !== data.allocationstrategyid) {
        hasChange = true;
        change.oldStrategy = ORDER_ALLOCATION_STRATEGIES[orig.allocationstrategyid];
        change.newStrategy = ORDER_ALLOCATION_STRATEGIES[data.allocationstrategyid];
        change.allocationStrategy = data.allocationstrategyid;
      }

      // Check is closed changes
      if (orig.isclosed !== data.isclosed) {
        hasChange = true;
        change.oldIsClosed = orig.isclosed;
        change.newIsClosed = data.isclosed;
        change.isclosed = data.isclosed;
      }

      // Normalize date strings to MM/DD/YYYY for comparison
      const normalizeDate = (v) => {
        const d = new Date(v);
        if (isNaN(d)) return v;
        const mm = (d.getMonth() + 1).toString().padStart(2, '0');
        const dd = d.getDate().toString().padStart(2, '0');
        const yyyy = d.getFullYear();
        return `${mm}/${dd}/${yyyy}`;
      };

      // Check start date changes
      const origStartDate = normalizeDate(orig.startdate);
      const newStartDate = normalizeDate(data.startdate);
      if (origStartDate !== newStartDate) {
        hasChange = true;
        change.oldStartDate = origStartDate;
        change.newStartDate = newStartDate;
        change.startDate = newStartDate;
      }

      // Check prep date changes
      const origPrepDate = normalizeDate(orig.prepdate);
      const newPrepDate = normalizeDate(data.prepdate);
      if (origPrepDate !== newPrepDate) {
        hasChange = true;
        change.oldPrepDate = origPrepDate;
        change.newPrepDate = newPrepDate;
        change.prepdate = newPrepDate;
      }

      // Check cancel date changes
      const origCancelDate = normalizeDate(orig.canceldate);
      const newCancelDate = normalizeDate(data.canceldate);
      if (origCancelDate !== newCancelDate) {
        hasChange = true;
        change.oldCancelDate = origCancelDate;
        change.newCancelDate = newCancelDate;
        change.canceldate = newCancelDate;
      }

      // Check item changes
      if (orig.upc !== data.upc) {
        hasChange = true;
        change.oldUPC = orig.upc;
        change.newUPC = data.upc;
        change.oldItemDesc = orig.itemdesc;
        change.newItemDesc = data.itemdesc;
        change.oldRate = orig.rate;
        change.newRate = data.rate;
        change.oldPrice = orig.price;
        change.newPrice = data.price;
        change.newItemId = data.netsuite_id; // This will be set when we find the item
        change.rate = data.rate;
        change.price = data.price;
        change.isclosed = data.isclosed;
      }

      if (hasChange) {
        console.log('Found change for row:', change.docnumber, change);
        changes.push(change);
      }
    });

    console.log('Total changes found:', changes.length);
    if (changes.length === 0) {
      message.info('No changes to save');
      return;
    }

    setPreviewData(changes);
    setPreviewVisible(true);
  };

  const previewColumnDefs = [
    { headerName: 'SO #', field: 'docnumber', },
    { headerName: 'PO #', field: 'ponumber', },
    { headerName: 'Customer', field: 'customername', },
    { headerName: 'Ordered', field: 'quantityordered', type: 'numericColumn', valueGetter: params => parseFloat(params.data?.quantityordered) || 0 },
    { headerName: 'Line #', field: 'lineid', hide: true },
    { headerName: 'UPC', field: 'upc', hide: true },
    { headerName: 'Item Desc', field: 'itemdesc', },
    {
      headerName: 'Allocation Strategy',
      children: [
        { headerName: 'Old', field: 'oldStrategy', },
        { headerName: 'New', field: 'newStrategy', cellStyle: { color: 'blue', fontWeight: 'bold' } }
      ]
    },
    {
      headerName: 'Start Date',
      children: [
        { headerName: 'Old', field: 'oldStartDate', comparator: dateComparator, valueFormatter: params => params.value?.seconds ? params.value.toDate().toISOString().split('T')[0] : '' },
        { headerName: 'New', field: 'newStartDate', cellStyle: { color: 'blue', fontWeight: 'bold' }, comparator: dateComparator }
      ]
    },
    {
      headerName: 'Prep Date',
      children: [
        { headerName: 'Old', field: 'oldPrepDate', comparator: dateComparator, valueFormatter: params => params.value?.seconds ? params.value.toDate().toISOString().split('T')[0] : '' },
        { headerName: 'New', field: 'newPrepDate', cellStyle: { color: 'blue', fontWeight: 'bold' }, comparator: dateComparator }
      ]
    },
    {
      headerName: 'Cancel Date',
      children: [
        { headerName: 'Old', field: 'oldCancelDate', comparator: dateComparator, valueFormatter: params => params.value?.seconds ? params.value.toDate().toISOString().split('T')[0] : '' },
        { headerName: 'New', field: 'newCancelDate', cellStyle: { color: 'blue', fontWeight: 'bold' }, comparator: dateComparator }
      ]
    },
    {
      headerName: 'Item',
      children: [
        { headerName: 'Old Desc', field: 'oldItemDesc', },
        { headerName: 'New Desc', field: 'newItemDesc', cellStyle: { color: 'blue', fontWeight: 'bold' } }
      ]
    },
    {
      headerName: 'Is Closed',
      children: [
        { headerName: 'Old', field: 'oldIsClosed', },
        { headerName: 'New', field: 'newIsClosed', cellStyle: { color: 'blue', fontWeight: 'bold' } }
      ]
    }
  ];

  // Intelligent batch processing for related changes
  const createBatchTasks = (changes) => {
    // Group changes by sales order for batch processing
    const changesBySO = changes.reduce((groups, change) => {
      const soNumber = change.docnumber;
      if (!groups[soNumber]) {
        groups[soNumber] = {
          soNumber,
          changes: [],
          rowKeys: [],
          updates: []
        };
      }
      groups[soNumber].changes.push(change);
      groups[soNumber].rowKeys.push(`${change.id}_${change.lineid}`);

      // Create update object
      const update = { orderId: change.id, lineNumber: change.linenumber, lineId: change.lineid };
      if (change.allocationStrategy !== undefined) update.allocationStrategy = change.allocationStrategy;
      if (change.startDate !== undefined) update.startDate = change.startDate;
      if (change.canceldate !== undefined) update.canceldate = change.canceldate;
      if (change.prepdate !== undefined) update.prepdate = change.prepdate;
      if (change.isclosed !== undefined) update.isclosed = change.isclosed;
      if (change.newItemId !== undefined) {
        update.newItemId = change.newItemId;
        update.newItemDesc = change.newItemDesc;
        update.newRate = change.newRate;
        update.newPrice = change.newPrice;
      }
      groups[soNumber].updates.push(update);

      return groups;
    }, {});

    return Object.values(changesBySO);
  };

  const confirmSave = async () => {
    // Close preview modal first
    setPreviewVisible(false);

    // Check authentication status
    if (!userData?.uid) {
      console.warn('User authentication not available during confirmSave, but proceeding with task creation');
    }

    // Create intelligent batches
    const batches = createBatchTasks(previewData);
    console.log(`Created ${batches.length} batch(es) for ${previewData.length} changes`);

    // Track which rows are being updated
    const allRowKeys = previewData.map(change => `${change.id}_${change.lineid}`);
    console.log('Processing batches for rows:', allRowKeys);

    addToSet('updatingRows', allRowKeys);
    // Clear any previous errors for these rows
    allRowKeys.forEach(rowKey => {
      removeFromSet('errorRows', rowKey);
      updateMap('rowErrors', rowKey, undefined);
    });

    // Process each batch
    const batchTasks = await Promise.all(batches.map(async (batch) => {
      const batchDescription = batch.changes.length === 1
        ? `${batch.soNumber} Line ${batch.changes[0].lineid}: ${batch.changes[0].customername}`
        : `${batch.soNumber}: ${batch.changes.length} line changes`;

      // Create enhanced Firestore task using the new single source of truth approach
      try {
        const taskDocId = await addTask({
          type: 'order_allocation_batch',
          description: batchDescription,
          rowKey: batch.rowKeys.join(','), // Comma-separated row keys
          data: {
            transactionId: batch.changes[0].id,
            lineId: batch.changes[0].lineid,
            docNumber: batch.soNumber,
            customerName: batch.changes[0].customername,
            originalData: batch.changes,
            updateData: batch.updates,
            batchSize: batch.changes.length,
            rowKeys: batch.rowKeys
          },
          priority: 'high',
          tags: ['order_allocation', 'batch_update', batch.soNumber],
          netsuiteTimeout: queueConfig?.DEFAULT_NETSUITE_TIMEOUT || 7200000,
          maxRetries: queueConfig?.MAX_RETRIES || 4,
          initialMessage: `Starting batch update for ${batch.soNumber} (${batch.changes.length} changes)`
        });

        return { taskDocId, batch };
      } catch (error) {
        console.error(`Failed to create task for batch ${batch.soNumber}:`, error);
        message.error(`Failed to create task for ${batch.soNumber}: ${error.message}`);
        throw error;
      }
    }));

    console.log(`Created ${batchTasks.length} batch tasks in Firestore`);

    // Automatically show the process queue when tasks are created
    if (batchTasks.length > 0 && !queueVisible) {
      toggleQueueVisibility();
    }

    // Process each batch
    batchTasks.forEach(async (batchInfo) => {
      const { taskDocId, batch } = batchInfo;

      try {
        // Update task status to processing - will be displayed in process queue
        await updateTaskStatus(taskDocId, 'processing', `Starting batch API call for ${batch.soNumber}`, { progress: 10 });
        console.log(`Processing batch for ${batch.soNumber}: ${batch.changes.length} changes`);

        // Send batch update to NetSuite
        const result = await api.updateSalesOrderOnCall({ updates: batch.updates });
        console.log(`NetSuite API accepted batch request for ${batch.soNumber}:`, result);

        await updateTaskStatus(taskDocId, 'api_success', `NetSuite API accepted batch request`, {
          progress: 60,
          apiResponse: result
        });

        // Skip optimistic updates - wait for actual NetSuite confirmation
        // This prevents data inconsistency between UI and actual NetSuite state
        console.log(`Skipping optimistic updates for ${batch.soNumber} - waiting for NetSuite confirmation`);

        // Move from updating to waiting for confirmation for all rows in batch
        removeFromSet('updatingRows', batch.rowKeys);
        addToSet('waitingForConfirmation', batch.rowKeys);

        console.log(`${batch.soNumber} - Batch API success, waiting for NetSuite confirmation...`);

        // Update task with expected changes for proper NetSuite confirmation
        const expectedChanges = batch.changes.map(change => ({
          transactionId: change.id,
          lineId: change.lineid,
          changes: {
            // Map the actual field changes that NetSuite should reflect
            ...(change.allocationStrategy !== undefined && { allocationstrategyid: change.allocationStrategy }),
            ...(change.startDate !== undefined && { startdate: change.startDate }),
            ...(change.canceldate !== undefined && { canceldate: change.canceldate }),
            ...(change.prepdate !== undefined && { prepdate: change.prepdate }),
            ...(change.isclosed !== undefined && { isclosed: change.isclosed }),
            ...(change.newItemId !== undefined && { itemid: change.newItemId })
          }
        }));

        // Single update with all necessary data for NetSuite confirmation tracking
        await updateTaskStatus(taskDocId, 'waiting_confirmation', 'Waiting for NetSuite confirmation', {
          progress: 80,
          expectedChanges: expectedChanges,
          originalData: batch.changes,
          targetData: batch.updates
        });

        // Start enhanced confirmation monitoring for the batch with realistic timeout
        const monitorId = startConfirmationMonitoring(taskDocId, batch.rowKeys[0], batch.changes[0], queueConfig?.CONFIRMATION_TIMEOUT || 180000);

        // Store monitor ID for potential cleanup
        updateMap('processingTimeouts', `${batch.rowKeys[0]}_monitorId`, monitorId);

        // Remove optimistic updates - let NetSuite confirmation drive UI updates
        console.log(`${batch.soNumber} - Batch API success, waiting for actual NetSuite confirmation...`);
        // In a real implementation, you'd have a NetSuite webhook or polling mechanism
        // to detect when the confirmation actually arrives

        // Ensure row state is cleared after task completion
        console.log(`Batch task completed for ${batch.soNumber}, ensuring row state is cleared`);
        removeFromSet('updatingRows', batch.rowKeys);
        removeFromSet('waitingForConfirmation', batch.rowKeys);
      } catch (error) {
        console.error(`Failed to process batch for ${batch.soNumber}:`, error);

        // Enhanced error handling with comprehensive recovery strategies
        const errorCategory = categorizeError(error);
        const isRetryable = ['network', 'timeout', 'netsuite_api'].includes(errorCategory);

        await handleTaskError(taskDocId, error, {
          retryCount: 0,
          maxRetries: queueConfig?.MAX_RETRIES || 4,
          rowKey: batch.rowKeys[0],
          change: batch.changes[0],
          errorCategory,
          isRetryable,
          batchSize: batch.changes.length,
          soNumber: batch.soNumber
        });

        // Provide user feedback based on error type
        if (errorCategory === 'network') {
          message.error(`Network error processing ${batch.soNumber}. Will retry automatically.`, 5);
        } else if (errorCategory === 'netsuite_api') {
          message.error(`NetSuite API error for ${batch.soNumber}. This may resolve automatically.`, 5);
        } else if (errorCategory === 'timeout') {
          message.warning(`Timeout processing ${batch.soNumber}. NetSuite may still be processing - will monitor for up to 3 minutes.`, 8);
        } else {
          message.error(`Error processing ${batch.soNumber}: ${error.message}`, 5);
        }
        // Task failure status will be displayed in process queue

        // Clear updating state and set error state for all rows in batch
        removeFromSet('updatingRows', batch.rowKeys);
        removeFromSet('waitingForConfirmation', batch.rowKeys);
        addToSet('errorRows', batch.rowKeys);

        // Set error messages for all rows in batch
        batch.rowKeys.forEach(rowKey => {
          updateMap('rowErrors', rowKey, error.message);
        });

        // Clear any confirmation monitors
        const monitorId = taskProcessingState.processingTimeouts.get(`${batch.rowKeys[0]}_monitorId`);
        if (monitorId) {
          stopConfirmationMonitoring(monitorId);
        }
        updateMap('processingTimeouts', `${batch.rowKeys[0]}_monitorId`, undefined);
      }
    });

    // Show enhanced success message with progress tracking
    const successMessage = `Started processing ${previewData.length} change${previewData.length !== 1 ? 's' : ''} - see process queue for real-time updates`;
    message.success({
      content: successMessage,
      duration: 4,
      style: {
        marginTop: '20px',
      },
    });

    // Show process queue automatically for better user experience
    if (!queueVisible) {
      toggleQueueVisibility();
    }

    setHasChanges(false);
  };

  // Update local data immediately with successful changes (optimistic update)
  const updateLocalDataWithChanges = (changes) => {
    console.log('Applying optimistic updates for changes:', changes.map(c => `${c.docnumber}-${c.lineid}`));

    // Skip optimistic updates if we're in the middle of a selective update from Firestore
    if (taskProcessingState.isSelectiveUpdate) {
      console.log('Skipping optimistic update - selective update in progress');
      return;
    }

    setRowData(currentRowData => {
      return currentRowData.map(row => {
        const change = changes.find(c => c.id === row.transactionid && c.lineid === row.lineid);
        if (!change) return row;

        const updatedRow = { ...row };

        // Apply the changes that were successfully saved to NetSuite
        if (change.allocationStrategy !== undefined) {
          updatedRow.allocationstrategyid = change.allocationStrategy;
          console.log(`Optimistically updated allocation strategy for ${change.docnumber}: ${change.oldStrategy} → ${change.newStrategy}`);
        }
        if (change.startDate !== undefined) {
          updatedRow.startdate = change.startDate;
          console.log(`Optimistically updated start date for ${change.docnumber}: ${change.oldStartDate} → ${change.newStartDate}`);
        }
        if (change.canceldate !== undefined) {
          updatedRow.canceldate = change.canceldate;
          console.log(`Optimistically updated cancel date for ${change.docnumber}: ${change.oldCancelDate} → ${change.newCancelDate}`);
        }
        if (change.prepdate !== undefined) {
          updatedRow.prepdate = change.prepdate;
          console.log(`Optimistically updated prep date for ${change.docnumber}: ${change.oldPrepDate} → ${change.newPrepDate}`);
        }
        if (change.isclosed !== undefined) {
          updatedRow.isclosed = change.isclosed;
          console.log(`Optimistically updated is closed for ${change.docnumber}: ${change.oldIsClosed} → ${change.newIsClosed}`);
        }
        if (change.newItemId !== undefined) {
          updatedRow.netsuite_id = change.newItemId;
          updatedRow.rate = change.newRate;
          updatedRow.price = change.newPrice;
          if (change.newItemDesc !== undefined) {
            updatedRow.itemdesc = change.newItemDesc;
          }
          if (change.newUPC !== undefined) {
            updatedRow.upc = change.newUPC;
          }
          console.log(`Optimistically updated item for ${change.docnumber}: ${change.oldUPC} → ${change.newUPC}`);
        }

        return updatedRow;
      });
    });

    // Also update the original row data to match (so changes don't show as "unsaved")
    setOriginalRowData(currentOriginalData => {
      return currentOriginalData.map(row => {
        const change = changes.find(c => c.id === row.transactionid && c.lineid === row.lineid);
        if (!change) return row;

        const updatedRow = { ...row };

        // Apply the same changes to original data
        if (change.allocationStrategy !== undefined) {
          updatedRow.allocationstrategyid = change.allocationStrategy;
        }
        if (change.startDate !== undefined) {
          updatedRow.startdate = change.startDate;
        }
        if (change.canceldate !== undefined) {
          updatedRow.canceldate = change.canceldate;
        }
        if (change.prepdate !== undefined) {
          updatedRow.prepdate = change.prepdate;
        }
        if (change.isclosed !== undefined) {
          updatedRow.isclosed = change.isclosed;
        }
        if (change.newItemId !== undefined) {
          updatedRow.netsuite_id = change.newItemId;
          updatedRow.rate = change.newRate;
          updatedRow.price = change.newPrice;
          if (change.newItemDesc !== undefined) {
            updatedRow.itemdesc = change.newItemDesc;
          }
          if (change.newUPC !== undefined) {
            updatedRow.upc = change.newUPC;
          }
        }

        return updatedRow;
      });
    });

    // Refresh only the affected rows to show updated styling
    if (gridRef.current?.api && changes.length > 0) {
      const affectedNodes = [];
      gridRef.current.api.forEachNode(node => {
        if (node.data) {
          const isAffected = changes.some(change =>
            change.id === node.data.transactionid && change.lineid === node.data.lineid
          );
          if (isAffected) {
            affectedNodes.push(node);
          }
        }
      });

      if (affectedNodes.length > 0) {
        gridRef.current.api.refreshCells({
          rowNodes: affectedNodes,
          force: true
        });
      }
    }
  };

  // Handle task retry from queue
  const handleTaskRetry = async (task) => {
    if (task.type === 'order_allocation_save' && task.data?.update) {
      // Handle individual task retry
      const rowKey = task.data.rowKey;
      const update = task.data.update;
      const change = task.data.change;

      console.log(`Retrying individual task for ${change.docnumber}`);

      // Mark row as updating again
      setUpdatingRows(prev => new Set([...prev, rowKey]));
      setErrorRows(prev => {
        const newSet = new Set(prev);
        newSet.delete(rowKey); // Clear error for retry
        return newSet;
      });
      setRowErrors(prev => {
        const newMap = new Map(prev);
        newMap.delete(rowKey);
        return newMap;
      });

      // Set processing timeout as failsafe
      setProcessingTimeout([rowKey]);

      try {
        await processTask(task.id, async (updateProgress) => {
          updateProgress(10);

          console.log(`Retrying individual row: ${change.docnumber} Line ${change.lineid}`);

          // Send individual update to NetSuite
          const result = await api.updateSalesOrderOnCall({ updates: [update] });
          console.log(`NetSuite API accepted retry request for ${change.docnumber}:`, result);
          updateProgress(60);

          // Skip optimistic update - wait for NetSuite confirmation
          console.log(`Skipping optimistic update for retry of ${change.docnumber}`);
          updateProgress(70);

          // Move from updating to waiting for confirmation
          setUpdatingRows(prev => {
            const newSet = new Set(prev);
            newSet.delete(rowKey);
            return newSet;
          });

          setWaitingForConfirmation(prev => new Set([...prev, rowKey]));
          console.log(`${change.docnumber} - Retry API success, now waiting for NetSuite confirmation...`);
          updateProgress(80);

          // Set a longer timeout for confirmation (2 minutes)
          const confirmationTimeoutId = setTimeout(() => {
            console.warn(`NetSuite confirmation timeout for retry ${change.docnumber}`);
            setWaitingForConfirmation(prev => {
              const newSet = new Set(prev);
              newSet.delete(rowKey);
              return newSet;
            });
            setErrorRows(prev => new Set([...prev, rowKey]));
            setRowErrors(prev => new Map(prev.set(rowKey, 'NetSuite confirmation timeout - changes may still be processing')));
          }, 120000); // 2 minutes

          // Store the confirmation timeout
          setProcessingTimeouts(prev => new Map(prev.set(`${rowKey}_confirmation`, confirmationTimeoutId)));

          // Don't complete the task yet - wait for NetSuite confirmation
          updateProgress(85);
          console.log(`Retry task for ${change.docnumber} API complete, but waiting for NetSuite confirmation before marking finished`);

          // Return a promise that resolves when NetSuite confirms
          return new Promise((resolve, reject) => {
            // Store the resolve function so we can call it when NetSuite confirms
            setProcessingTimeouts(prev => new Map(prev.set(`${rowKey}_resolve`, resolve)));

            // Also store reject for timeout case
            setTimeout(() => {
              reject(new Error('NetSuite confirmation timeout'));
            }, 120000); // 2 minutes
          });
        });

        // Success will be handled when NetSuite confirms via checkForNetSuiteConfirmations
        console.log(`Retry task for ${change.docnumber} completed, waiting for NetSuite confirmation`);
      } catch (err) {
        // Handle individual retry error
        clearProcessingTimeouts([rowKey]);
        setErrorRows(prev => new Set([...prev, rowKey]));

        let errorMessage = 'Unknown error occurred';
        if (err?.response?.data?.error) {
          errorMessage = err.response.data.error;
        } else if (err?.response?.data?.message) {
          errorMessage = err.response.data.message;
        } else if (err?.message) {
          errorMessage = err.message;
        } else if (typeof err === 'string') {
          errorMessage = err;
        }

        setRowErrors(prev => new Map(prev.set(rowKey, errorMessage)));
        setUpdatingRows(prev => {
          const newSet = new Set(prev);
          newSet.delete(rowKey);
          return newSet;
        });

        message.error(`Retry failed for ${change.docnumber}: ${errorMessage}`);
        throw new Error(errorMessage);
      }
    } else if (task.type === 'order_allocation_save' && task.data?.updates) {
    // Handle legacy bulk task retry
      const rowKeys = task.data?.rowKeys || [];

      // Mark rows as updating again
      setUpdatingRows(prev => new Set([...prev, ...rowKeys]));
      setErrorRows(prev => {
        const newSet = new Set(prev);
        rowKeys.forEach(key => newSet.delete(key)); // Clear errors for retry
        return newSet;
      });

      // Set processing timeout as failsafe
      setProcessingTimeout(rowKeys);

      try {
        await processTask(task.id, async (updateProgress) => {
          updateProgress(10);

          const result = await api.updateSalesOrderOnCall({ updates: task.data.updates });
          updateProgress(90);

          await new Promise(resolve => setTimeout(resolve, 500));
          updateProgress(100);

          setHasChanges(false);

          return result;
        });

        // Success handling - always executed if processTask completes successfully
        clearProcessingTimeouts(rowKeys); // Clear timeouts first

        setUpdatingRows(prev => {
          const newSet = new Set(prev);
          rowKeys.forEach(key => newSet.delete(key));
          return newSet;
        });

        setSuccessRows(prev => new Set([...prev, ...rowKeys]));

        // Clear success state after animation
        setTimeout(() => {
          setSuccessRows(prev => {
            const newSet = new Set(prev);
            rowKeys.forEach(key => newSet.delete(key));
            return newSet;
          });
        }, 3000);

        // Skip optimistic updates - wait for NetSuite confirmation
        console.log(`Skipping optimistic updates for retry task - waiting for NetSuite confirmation`);

        message.success(`Successfully retried and saved ${task.data?.changes?.length || 0} changes`);
      } catch (err) {
        // Extract meaningful error message for retry feedback
        let errorMessage = 'Unknown error occurred';
        if (err?.response?.data?.error) {
          errorMessage = err.response.data.error;
        } else if (err?.response?.data?.message) {
          errorMessage = err.response.data.message;
        } else if (err?.message) {
          errorMessage = err.message;
        } else if (typeof err === 'string') {
          errorMessage = err;
        }

        // Clear timeouts and mark rows as having errors again with specific messages
        clearProcessingTimeouts(rowKeys);

        setErrorRows(prev => new Set([...prev, ...rowKeys]));
        setRowErrors(prev => {
          const newMap = new Map(prev);
          rowKeys.forEach(key => newMap.set(key, errorMessage));
          return newMap;
        });
        setUpdatingRows(prev => {
          const newSet = new Set(prev);
          rowKeys.forEach(key => newSet.delete(key));
          return newSet;
        });

        message.error(`Retry failed: ${errorMessage}`);
        throw new Error(errorMessage);
      }
    }
  };

  const handleRevert = () => {
    setRowData(originalRowData.map(r => ({ ...r })));  // reset to original clones
    setHasChanges(false);

    // Clear all timeouts first
    taskProcessingState.processingTimeouts.forEach(timeoutId => clearTimeout(timeoutId));

    // Clear all states when reverting
    updateTaskProcessingState({
      errorRows: new Set(),
      successRows: new Set(),
      rowErrors: new Map(),
      updatingRows: new Set(),
      waitingForConfirmation: new Set(),
      processingTimeouts: new Map()
    });
  };

  const clearRowErrors = () => {
    updateTaskProcessingState({
      errorRows: new Set(),
      rowErrors: new Map()
    });
    message.success('Error states cleared');
  };

  // Debug function to manually clear all processing states
  const clearAllProcessingStates = () => {
    console.log('Manually clearing all processing states:', {
      updatingRows: Array.from(taskProcessingState.updatingRows),
      waitingForConfirmation: Array.from(taskProcessingState.waitingForConfirmation),
      errorRows: Array.from(taskProcessingState.errorRows),
      successRows: Array.from(taskProcessingState.successRows),
      processingTimeouts: taskProcessingState.processingTimeouts.size
    });

    // Clear all timeouts
    taskProcessingState.processingTimeouts.forEach(timeoutId => clearTimeout(timeoutId));

    // Clear all states
    updateTaskProcessingState({
      updatingRows: new Set(),
      waitingForConfirmation: new Set(),
      errorRows: new Set(),
      successRows: new Set(),
      rowErrors: new Map(),
      processingTimeouts: new Map()
    });

    message.success('All processing states cleared');
  };

  // Set a timeout for processing rows to prevent infinite processing state
  const setProcessingTimeout = (rowKeys, timeoutMs = 30000) => {
    rowKeys.forEach(rowKey => {
      // Clear any existing timeout for this row
      const existingTimeout = taskProcessingState.processingTimeouts.get(rowKey);
      if (existingTimeout && typeof existingTimeout === 'number') {
        clearTimeout(existingTimeout);
      }

      // Set new timeout
      const timeoutId = setTimeout(() => {
        console.warn(`Processing timeout for row ${rowKey} - clearing updating state`);

        setUpdatingRows(prev => {
          const newSet = new Set(prev);
          newSet.delete(rowKey);
          return newSet;
        });

        setErrorRows(prev => new Set([...prev, rowKey]));
        setRowErrors(prev => new Map(prev.set(rowKey, 'Processing timed out after 30 seconds')));

        // Clear the timeout from our map
        setProcessingTimeouts(prev => {
          const newMap = new Map(prev);
          newMap.delete(rowKey);
          return newMap;
        });

        message.warning(`Row processing timed out - please try again`);
      }, timeoutMs);

      // Store the timeout ID with start time for auto-clear mechanism
      updateMap('processingTimeouts', rowKey, {
        timeoutId,
        startTime: Date.now()
      });
    });
  };

  // Clear processing timeouts for completed rows
  const clearProcessingTimeouts = (rowKeys) => {
    rowKeys.forEach(rowKey => {
      const timeoutData = taskProcessingState.processingTimeouts.get(rowKey);
      if (timeoutData) {
        if (typeof timeoutData === 'number') {
          clearTimeout(timeoutData);
        } else if (timeoutData.timeoutId) {
          clearTimeout(timeoutData.timeoutId);
        }
      }
    });

    rowKeys.forEach(key => {
      updateMap('processingTimeouts', key, undefined);
    });
  };

  // Retry a specific row
  const retryRow = async (rowData) => {
    const rowKey = `${rowData.transactionid}_${rowData.lineid}`;

    // Find the original change data for this row
    const originalRow = originalRowData.find(r =>
      r.transactionid === rowData.transactionid && r.lineid === rowData.lineid
    );

    if (!originalRow) {
      message.error('Cannot find original row data for retry');
      return;
    }

    // Create change object
    const change = {
      id: rowData.transactionid,
      lineid: rowData.lineid,
      linenumber: rowData.linenumber,
      docnumber: rowData.docnumber,
      ponumber: rowData.ponumber,
      customername: rowData.customername,
      quantityordered: rowData.quantityordered,
      quantitycommitted: rowData.quantitycommitted,
      upc: rowData.upc,
      itemdesc: rowData.itemdesc
    };

    // Check for changes and build update object
    const upd = { orderId: change.id, lineNumber: change.linenumber, lineId: change.lineid };
    let hasChanges = false;

    if (originalRow.allocationstrategyid !== rowData.allocationstrategyid) {
      upd.allocationStrategy = rowData.allocationstrategyid;
      hasChanges = true;
    }
    if (originalRow.isclosed !== rowData.isclosed) {
      upd.isclosed = rowData.isclosed;
      hasChanges = true;
    }
    if (originalRow.startdate !== rowData.startdate) {
      upd.startDate = rowData.startdate;
      hasChanges = true;
    }
    if (originalRow.prepdate !== rowData.prepdate) {
      upd.prepdate = rowData.prepdate;
      hasChanges = true;
    }
    if (originalRow.canceldate !== rowData.canceldate) {
      upd.canceldate = rowData.canceldate;
      hasChanges = true;
    }
    if (originalRow.upc !== rowData.upc) {
      upd.newItemId = rowData.netsuite_id;
      upd.newItemDesc = rowData.itemdesc;
      upd.newRate = rowData.rate;
      upd.newPrice = rowData.price;
      hasChanges = true;
    }

    if (!hasChanges) {
      message.info('No changes detected for this row');
      return;
    }

    // Set row as updating and clear error
    addToSet('updatingRows', rowKey);
    removeFromSet('errorRows', rowKey);
    updateMap('rowErrors', rowKey, undefined);
    removeFromSet('successRows', rowKey);

    // Set processing timeout as failsafe
    setProcessingTimeout([rowKey]);

    try {
      console.log(`Retrying individual row: ${rowData.docnumber} Line ${rowData.lineid}`);

      // Send individual update to NetSuite
      const result = await api.updateSalesOrderOnCall({ updates: [upd] });
      console.log(`NetSuite API accepted retry request for ${rowData.docnumber}:`, result);

      // Skip optimistic update - wait for NetSuite confirmation
      console.log(`Skipping optimistic update for individual retry of ${rowData.docnumber}`);

      // Move from updating to waiting for confirmation
      removeFromSet('updatingRows', rowKey);
      addToSet('waitingForConfirmation', rowKey);
      console.log(`${rowData.docnumber} - Retry API success, now waiting for NetSuite confirmation...`);

      // Set a longer timeout for confirmation (2 minutes)
      const confirmationTimeoutId = setTimeout(() => {
        console.warn(`NetSuite confirmation timeout for retry ${rowData.docnumber}`);
        removeFromSet('waitingForConfirmation', rowKey);
        addToSet('errorRows', rowKey);
        updateMap('rowErrors', rowKey, 'NetSuite confirmation timeout - changes may still be processing');
      }, 120000); // 2 minutes

      // Store the confirmation timeout
      updateMap('processingTimeouts', `${rowKey}_confirmation`, confirmationTimeoutId);

      console.log(`Individual retry for ${rowData.docnumber} API complete, waiting for NetSuite confirmation`);
    } catch (err) {
      // Clear timeout and mark as error with specific message
      clearProcessingTimeouts([rowKey]);
      addToSet('errorRows', rowKey);

      let errorMessage = 'Unknown error occurred';
      if (err?.response?.data?.error) {
        errorMessage = err.response.data.error;
      } else if (err?.response?.data?.message) {
        errorMessage = err.response.data.message;
      } else if (err?.message) {
        errorMessage = err.message;
      } else if (typeof err === 'string') {
        errorMessage = err;
      }

      updateMap('rowErrors', rowKey, errorMessage);
      message.error(`Failed to update row: ${errorMessage}`);
    } finally {
      // Clear updating state
      removeFromSet('updatingRows', rowKey);
    }
  };
  // Mark as changed and refresh style when a cell edit occurs
  const onCellValueChanged = (params) => {
    setHasChanges(true);

    // Clear any error/success state for this row since user is making new changes
    if (params.data) {
      const rowKey = `${params.data.transactionid}_${params.data.lineid}`;
      removeFromSet('errorRows', rowKey);
      removeFromSet('successRows', rowKey);
      removeFromSet('waitingForConfirmation', rowKey);
      updateMap('rowErrors', rowKey, undefined);
    }

    // refresh the cells to reapply cellStyle
    params.api.refreshCells({
      rowNodes: [params.node],
      columns: ['allocationstrategyid', 'startdate', 'canceldate', 'prepdate', 'isclosed']
    });
  };

  const handleViewChange = async (value) => {
    setSelectedViewId(value);
    try {
      await setDoc(doc(db, 'users', userData.id), {
        lastOrderAllocationView: value || null,
      }, { merge: true });
      
      if (value) {
        await applyViewState(value);
      } else {
        // Clear view - reset grid state
        if (gridRef.current?.api) {
          const api = gridRef.current.api;
          api.setState({});
        }
      }
    } catch (error) {
      console.error('Error updating user view preference:', error);
      message.error('Failed to update view preference');
    }
  };

  useEffect(() => {
    if (!gridRef.current?.api) return;

    const currentFilters = gridRef.current.api.getFilterModel() || {};

    // Handle SO number filter
    if (searchSO.sonumber) {
      currentFilters.docnumber = {
        filterType: 'text',
        type: 'multiValue',
        filter: searchSO.sonumber
      };
    } else {
      delete currentFilters.docnumber;
    }

    // Handle UPC filter
    if (searchSO.upc) {
      currentFilters.upc = {
        filterType: 'text',
        type: 'multiValue',
        filter: searchSO.upc
      };
    } else {
      delete currentFilters.upc;
    }

    // Handle PO number filter
    if (searchSO.ponumber) {
      currentFilters.ponumber = {
        filterType: 'text',
        type: 'multiValue',
        filter: searchSO.ponumber
      };
    } else {
      delete currentFilters.ponumber;
    }

    gridRef.current.api.setFilterModel(currentFilters);
  }, [searchSO]);

  // Apply the user's last used view when the component mounts and grid is ready
  useEffect(() => {
    if (savedViews.length > 0 && gridRef.current?.api && selectedViewId) {
      applyViewState(selectedViewId);
      setDoc(doc(db, 'users', userData.id), {
        lastOrderAllocationView: selectedViewId
      }, { merge: true });
    }
  }, [savedViews, selectedViewId]);

  return (
    <>
      <Card
        size="small"
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <Text strong>Order Allocation</Text>
            <Text type="secondary">({rowData.length} lines) ({new Set(rowData.map(r => r.transactionid)).size} orders)</Text>
            <Text type="secondary">Last Updated: {
              lastUpdated && lastUpdated?.seconds ? lastUpdated?.toDate().toLocaleString() : 'Never'
            }</Text>
            {taskProcessingState.updatingRows.size > 0 && (
              <Tag color="processing" style={{ margin: 0 }}>
                Updating {taskProcessingState.updatingRows.size} row{taskProcessingState.updatingRows.size !== 1 ? 's' : ''}
              </Tag>
            )}
            {taskProcessingState.waitingForConfirmation.size > 0 && (
              <Tag color="orange" style={{ margin: 0 }}>
                Waiting for {taskProcessingState.waitingForConfirmation.size} confirmation{taskProcessingState.waitingForConfirmation.size !== 1 ? 's' : ''}
              </Tag>
            )}
            {taskProcessingState.errorRows.size > 0 && (
              <Tag color="error" style={{ margin: 0 }}>
                {taskProcessingState.errorRows.size} error{taskProcessingState.errorRows.size !== 1 ? 's' : ''}
              </Tag>
            )}
            <Button type="link" onClick={triggerRefreshOpenOrders} loading={isLoading || openOrdersRefreshing}>Refresh</Button>
            {hasChanges && (
              <Tag color="orange" style={{ margin: 0 }}>
                Unsaved Changes
              </Tag>
            )}
          </div>
        }
        extra={
          <Space direction="vertical" size={4}>
            <Space>
              <Select
                style={{ minWidth: 250 }}
                placeholder="Select Saved View"
                value={selectedViewId}
                onChange={handleViewChange}
                options={savedViews.map(v => ({ label: v.name, value: v.id }))}
                allowClear
                loading={isLoading}
                onClear={() => {
                  setSelectedViewId(null);
                }}
                optionRender={(option) => {
                  const savedView = savedViews.find(v => v.id === option.key);
                  if (!savedView) return null;
                  return <Space style={{ display: 'flex', justifyContent: 'space-between', gap: 4 }}>
                    <span>{option.label}</span>
                    {savedView.createdBy === userData?.id && (
                      <Popconfirm title="Are you sure you want to delete this view?" onConfirm={() => handleDeleteView(savedView.id)}>
                        <DeleteOutlined style={{ color: 'red' }} />
                      </Popconfirm>
                    )}
                    {savedView.createdBy === userData?.id && (<Tooltip title={savedView.isPublic ? 'Public View' : 'Private View'}>
                      <ShareOutlined
                        className={savedView.isPublic ? 'shared' : 'not-shared'}
                        onClick={() => handleToggleViewVisibility(savedView.id)}
                      />
                    </Tooltip>)
                    }
                  </Space>;
                }}
              />
              <Tooltip title="Create a custom view with your current settings">
                <Button
                  onClick={handleSaveView}
                  icon={<SaveOutlined />}
                  type="default"
                  style={{
                    fontWeight: 500,
                    borderColor: '#1890ff',
                    color: '#1890ff'
                  }}
                >
                  Save Custom View
                </Button>
              </Tooltip>
              <Button onClick={handleRevert} disabled={!hasChanges}>Revert</Button>
              <Button type="primary" onClick={handleSave} disabled={!hasChanges}>Save</Button>
            </Space>
          </Space>
        }
      >
        <div style={{ height: 'calc(90vh - 25px)', width: '100%' }}>
          {rowData.length > 0 && (
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 8 }}>
              <Space>
                <Popover
                  content={bulkActionContent}
                  title="Bulk Actions"
                  trigger="click"
                  open={bulkActionsVisible}
                  onOpenChange={setBulkActionsVisible}
                >
                  <Button
                    // disabled={selectedRows === 0}
                    title={selectedRows === 0 ? "Select at least one row to use bulk actions" : "Bulk Actions"}
                  >
                    Bulk Actions
                  </Button>
                </Popover>
                <Button
                  onClick={clearAllFilters}
                  disabled={!hasActiveFilters}
                  title={!hasActiveFilters ? "No active filters to clear" : "Clear all active filters"}
                >
                  Clear All Filters
                </Button>
                {taskProcessingState.errorRows.size > 0 && (
                  <Button
                    onClick={clearRowErrors}
                    danger
                    title={`Clear error states from ${taskProcessingState.errorRows.size} row${taskProcessingState.errorRows.size !== 1 ? 's' : ''}`}
                  >
                    Clear Errors ({taskProcessingState.errorRows.size})
                  </Button>
                )}
                {taskProcessingState.updatingRows.size > 0 && (
                  <Button
                    onClick={clearAllProcessingStates}
                    type="dashed"
                    danger
                    title={`Force clear all processing states (${taskProcessingState.updatingRows.size} updating)`}
                  >
                    Clear Processing ({taskProcessingState.updatingRows.size})
                  </Button>
                )}
                <Input
                  placeholder="Search by SO #s"
                  value={searchSO.sonumber}
                  onChange={(e) => (setSearchSO({ ...searchSO, sonumber: e.target.value }))}
                  style={{ width: '200px' }}
                  allowClear
                />
                <Input
                  placeholder="Search by UPCs"
                  value={searchSO.upc}
                  onChange={(e) => (setSearchSO({ ...searchSO, upc: e.target.value }))}
                  style={{ width: '200px' }}
                  allowClear
                />
                <Input
                  placeholder="Search by PO #s"
                  value={searchSO.ponumber}
                  onChange={(e) => (setSearchSO({ ...searchSO, ponumber: e.target.value }))}
                  style={{ width: '200px' }}
                  allowClear
                />
              </Space>
              <ProcessQueueButton
                tasks={tasks}
                onClick={toggleQueueVisibility}
                visible={true}
                connectionStatus={connectionStatus}
              />
            </div>
          )}
          <div style={{ height: 'calc(90vh - 25px)', width: '100%' }}>
            <AgGridReact
              ref={gridRef}
              theme={themeBalham}
              rowData={rowData}
              columnDefs={columnDefs}
              onCellValueChanged={onCellValueChanged}
              onSelectionChanged={onSelectionChanged}
              onFilterChanged={(e) => {
                checkActiveFilters(e);
              }}
              gridOptions={{
                grandTotalRow: 'pinnedBottom'
              }}
              rowSelection="multiRow"
              rowMultiSelectWithClick={false}
              suppressRowClickSelection={true}
              cellSelection={true}
              autoSizeStrategy={{ type: 'fitGridWidth' }}
              defaultColDef={{
                resizable: true,
                sortable: true,
                filter: true
              }}

              rowClassRules={{
                'ag-row-even': params => params.rowIndex % 2 === 0,
                'ag-grid-syncing': params => params.data?.syncStatus !== 'completed',
                'ag-row-updating': params => {
                  if (!params.data) return false;
                  const rowKey = `${params.data.transactionid}_${params.data.lineid}`;
                  return taskProcessingState.updatingRows.has(rowKey);
                },
                'ag-row-waiting-confirmation': params => {
                  if (!params.data) return false;
                  const rowKey = `${params.data.transactionid}_${params.data.lineid}`;
                  return taskProcessingState.waitingForConfirmation.has(rowKey);
                },
                'ag-row-error': params => {
                  if (!params.data) return false;
                  const rowKey = `${params.data.transactionid}_${params.data.lineid}`;
                  return taskProcessingState.errorRows.has(rowKey);
                },
                'ag-row-success': params => {
                  if (!params.data) return false;
                  const rowKey = `${params.data.transactionid}_${params.data.lineid}`;
                  return taskProcessingState.successRows.has(rowKey);
                }
              }}

              rowGroupPanelShow="always"
              groupDefaultExpanded={1}

              suppressAggFuncInHeader={true}

              sideBar={{
                toolPanels: [
                  {
                    id: 'columns',
                    labelDefault: 'Columns',
                    labelKey: 'columns',
                    iconKey: 'columns',
                    toolPanel: 'agColumnsToolPanel',
                  },
                  {
                    id: 'filters',
                    labelDefault: 'Filters',
                    labelKey: 'filters',
                    iconKey: 'filter',
                    toolPanel: 'agFiltersToolPanel',
                  }
                ],
                // defaultToolPanel: 'filters',
              }}
              pagination
              paginationPageSize={userData?.pageSize || 50}
              size="small"
            />
          </div>
        </div>

        {/* Enhanced Process Queue with Chrome Downloads Manager styling */}
        <ProcessQueue
          tasks={tasks}
          onTaskUpdate={updateTask}
          onTaskRemove={removeTask}
          onTaskRetry={handleTaskRetry}
          onTaskCancel={(task) => {
            updateTask(task.id, {
              status: 'cancelled',
              message: 'Cancelled by user',
              completedAt: Date.now()
            });
          }}
          onClearCompleted={clearAllTasks}
          onRetryAllFailed={() => {
            const failedTasks = tasks.filter(t =>
              t.status === 'failed' ||
              t.status === 'timeout' ||
              t.status === 'cancelled'
            );
            failedTasks.forEach(task => retryTask(task));
          }}
          visible={queueVisible}
          onClose={hideQueue}
          connectionStatus={connectionStatus}
        />

        <Modal
          title="Confirm Changes"
          open={previewVisible}
          destroyOnClose
          onOk={confirmSave}
          onCancel={() => setPreviewVisible(false)}
          width={"90%"}
          okButtonProps={{ loading: taskProcessingState.updatingNetsuite }}
          cancelButtonProps={{ loading: taskProcessingState.updatingNetsuite }}
        >
          <div style={{ height: '400px', width: '100%' }}>
            <AgGridReact
              columnDefs={previewColumnDefs}
              theme={themeBalham}
              rowData={previewData}
              defaultColDef={{
                resizable: true,
                sortable: true,
                filter: true
              }}
              autoSizeStrategy={{ type: 'fitGridWidth' }}
              pagination
            />
          </div>
        </Modal>
      </Card>
    </>
  );
};

export default OrderAllocation; 