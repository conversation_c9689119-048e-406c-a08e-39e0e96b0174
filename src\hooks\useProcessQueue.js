import { useState, useCallback, useRef, useEffect } from 'react';
import { TASK_STATUS, TASK_STEPS, ERROR_CATEGORIES } from '../components/ProcessQueue';
import {
  collection,
  query,
  where,
  orderBy,
  onSnapshot,
  doc,
  updateDoc,
  deleteDoc,
  addDoc,
  serverTimestamp,
  writeBatch,
  limit,
  getDoc,
  getDocs
} from 'firebase/firestore';
import { db } from '../pages/firebase';

// Enhanced task configuration for Order Allocation process queue tracking
const TASK_CONFIG = {
  // NetSuite operations can take up to 2 hours
  DEFAULT_NETSUITE_TIMEOUT: 2 * 60 * 60 * 1000, // 2 hours
  CONFIRMATION_TIMEOUT: 2 * 60 * 60 * 1000, // 2 hours for confirmation
  EXTENDED_TIMEOUT: 2 * 60 * 60 * 1000, // 2 hours for complex operations
  HEALTH_CHECK_INTERVAL: 30 * 1000, // 30 seconds
  RETRY_DELAYS: {
    [ERROR_CATEGORIES.NETWORK]: [1000, 2000, 5000, 10000], // 1s, 2s, 5s, 10s
    [ERROR_CATEGORIES.TIMEOUT]: [5000, 15000, 30000, 60000], // 5s, 15s, 30s, 1m
    [ERROR_CATEGORIES.NETSUITE_API]: [2000, 5000, 10000, 30000], // 2s, 5s, 10s, 30s
    [ERROR_CATEGORIES.PERMISSION]: [10000, 30000, 60000, 120000], // 10s, 30s, 1m, 2m
    [ERROR_CATEGORIES.SYSTEM]: [1000, 3000, 8000, 20000] // 1s, 3s, 8s, 20s
  },
  MAX_RETRIES: 4,
  CLEANUP_RETENTION_HOURS: 24
};

// Helper function to categorize errors for better handling
const categorizeError = (error) => {
  const errorStr = typeof error === 'string' ? error : error?.message || '';

  if (errorStr.includes('network') || errorStr.includes('fetch')) {
    return ERROR_CATEGORIES.NETWORK;
  }
  if (errorStr.includes('timeout')) {
    return ERROR_CATEGORIES.TIMEOUT;
  }
  if (errorStr.includes('permission') || errorStr.includes('unauthorized')) {
    return ERROR_CATEGORIES.PERMISSION;
  }
  if (errorStr.includes('netsuite') || errorStr.includes('NetSuite')) {
    return ERROR_CATEGORIES.NETSUITE_API;
  }

  return ERROR_CATEGORIES.SYSTEM;
};

// Helper function to generate step messages
const getStepMessage = (step, updates) => {
  const messages = {
    [TASK_STEPS.CREATED]: 'Task created and queued for processing',
    [TASK_STEPS.API_CALL_STARTED]: 'Sending request to NetSuite...',
    [TASK_STEPS.API_CALL_SUCCESS]: 'NetSuite API call successful',
    [TASK_STEPS.API_CALL_FAILED]: `NetSuite API call failed: ${updates.error || 'Unknown error'}`,
    [TASK_STEPS.WAITING_NETSUITE_CONFIRMATION]: 'Waiting for NetSuite to confirm changes...',
    [TASK_STEPS.NETSUITE_CONFIRMED]: 'NetSuite confirmed changes successfully',
    [TASK_STEPS.CONFIRMATION_TIMEOUT]: 'Timeout waiting for NetSuite confirmation',
    [TASK_STEPS.COMPLETED]: 'Task completed successfully',
    [TASK_STEPS.FAILED]: `Task failed: ${updates.error || 'Unknown error'}`,
    [TASK_STEPS.RETRY_INITIATED]: `Retrying task (attempt ${updates.retryCount || 1})`
  };

  return updates.message || messages[step] || `Task step: ${step}`;
};

/**
 * Enhanced Process Queue Hook with Firestore Single Source of Truth
 * Manages task lifecycle from creation to completion with real-time sync
 * Provides Order Allocation process queue with individual task tracking and 100% reliability
 * @param {Object} userData - User data object containing uid for task ownership
 * @return {Object} Object containing task management functions and state
 */
const useProcessQueue = (userData) => {
  // Debug userData in hook
  // console.log('🎣 useProcessQueue received userData:', userData); // Reduced logging noise

  // State management - Firestore is the single source of truth
  const [tasks, setTasks] = useState([]);
  const [queueVisible, setQueueVisible] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState('connected');

  // Refs for cleanup and monitoring
  const unsubscribeRef = useRef(null);
  const timeoutRefs = useRef(new Map()); // Track timeout references
  const healthCheckRef = useRef(null);
  const taskCacheRef = useRef(new Map()); // Cache for quick lookups

  // Enhanced cleanup on unmount
  useEffect(() => {
    return () => {
      // Clear all timeouts
      timeoutRefs.current.forEach(timeoutId => clearTimeout(timeoutId));
      timeoutRefs.current.clear();

      // Clear health check
      if (healthCheckRef.current) {
        clearInterval(healthCheckRef.current);
      }

      // Clear task cache
      taskCacheRef.current.clear();
    };
  }, []);

  // Enhanced Firestore listener with single source of truth
  useEffect(() => {
    console.log('🔄 Setting up enhanced Firestore listener for process queue tasks');
    setIsLoading(true);
    setConnectionStatus('connecting');

    // Use consistent user ID logic for both query and task creation
    const userId = userData?.uid || 'anonymous';
    console.log('📋 Querying tasks for userId:', userId);

    const q = query(
      collection(db, 'tasks'),
      where('userId', '==', userId),
      where('createdAt', '>=', new Date(Date.now() - TASK_CONFIG.CLEANUP_RETENTION_HOURS * 60 * 60 * 1000)),
      orderBy('createdAt', 'desc')
    );

    const unsubscribe = onSnapshot(q,
      (snapshot) => {
        try {
          console.log(`📥 Firestore snapshot received: ${snapshot.docs.length} documents`);
          const firestoreTasks = snapshot.docs.map(doc => {
            const data = doc.data();
            console.log(`📄 Processing task document ${doc.id}:`, data);
            const task = {
              id: doc.id, // Use Firestore document ID as primary identifier
              firestoreId: doc.id,
              ...data,
              // Normalize timestamps for consistency
              createdAt: data.createdAt?.toMillis?.() || data.createdAt || Date.now(),
              updatedAt: data.updatedAt?.toMillis?.() || data.updatedAt || Date.now(),
              startedAt: data.startedAt?.toMillis?.() || data.startedAt || null,
              completedAt: data.completedAt?.toMillis?.() || data.completedAt || null,
              lastUpdatedAt: data.lastUpdatedAt?.toMillis?.() || data.lastUpdatedAt || Date.now(),
            };

            // Update task cache for quick lookups
            taskCacheRef.current.set(doc.id, task);
            return task;
          });

          console.log(`✅ Process queue received ${firestoreTasks.length} tasks from Firestore`);
          setTasks(firestoreTasks);
          setIsLoading(false);
          setConnectionStatus('connected');

          // Auto-show queue if there are active tasks
          const activeTasks = firestoreTasks.filter(task =>
            task.status === TASK_STATUS.QUEUED ||
            task.status === TASK_STATUS.PROCESSING ||
            task.status === TASK_STATUS.WAITING_CONFIRMATION ||
            task.status === TASK_STATUS.RETRYING
          );

          if (activeTasks.length > 0 && !queueVisible) {
            setQueueVisible(true);
          }
        } catch (error) {
          console.error('❌ Error processing Firestore snapshot:', error);
          setConnectionStatus('error');
        }
      },
      (error) => {
        console.error('❌ Error listening to Firestore tasks:', error);
        setIsLoading(false);
        setConnectionStatus('error');

        // Implement reconnection logic
        setTimeout(() => {
          console.log('🔄 Attempting to reconnect to Firestore...');
          setConnectionStatus('reconnecting');
        }, 5000);
      }
    );

    unsubscribeRef.current = unsubscribe;

    return () => {
      if (unsubscribeRef.current) {
        unsubscribeRef.current();
        unsubscribeRef.current = null;
      }
    };
  }, [userData?.uid]);

  // Enhanced addTask - Creates task directly in Firestore (single source of truth)
  const addTask = useCallback(async (taskConfig) => {
    if (!userData?.uid) {
      console.warn('User authentication not available, using fallback user ID');
      // Use a fallback user ID or anonymous user for task creation
      // This ensures the system continues to work even if authentication is temporarily unavailable
    }

    try {
      const now = Date.now();
      const userId = userData?.uid || 'anonymous';

      console.log('🔨 Creating new task with userId:', userId);
      console.log('🔨 Task config:', taskConfig);

      // Create task document directly in Firestore
      const taskData = {
        type: taskConfig.type || 'generic',
        description: taskConfig.description || 'Processing task',
        status: TASK_STATUS.QUEUED,
        data: taskConfig.data || {},

        // Enhanced timing with server timestamps
        createdAt: serverTimestamp(),
        startedAt: null,
        completedAt: null,
        lastUpdatedAt: serverTimestamp(),

        // Progress tracking
        progress: 0,
        currentStep: TASK_STEPS.CREATED,

        // Error handling with enhanced retry configuration
        error: null,
        errorCategory: null,
        retryCount: 0,
        maxRetries: taskConfig.maxRetries || TASK_CONFIG.MAX_RETRIES,

        // NetSuite tracking with extended timeouts
        netsuiteRequestId: null,
        netsuiteConfirmed: false,
        confirmationTimeout: null,
        netsuiteTimeout: taskConfig.netsuiteTimeout || TASK_CONFIG.DEFAULT_NETSUITE_TIMEOUT,

        // User context and metadata
        userId: userId,
        rowKey: taskConfig.rowKey || null,
        priority: taskConfig.priority || 'normal',
        tags: taskConfig.tags || [],

        // Enhanced steps tracking with detailed history
        steps: {
          [`step_${now}`]: {
            step: TASK_STEPS.CREATED,
            timestamp: serverTimestamp(),
            message: taskConfig.initialMessage || 'Task created in process queue',
            progress: 0,
            metadata: {
              userAgent: navigator.userAgent,
              timestamp: now,
              source: 'process_queue'
            }
          }
        },

        // Additional configuration
        ...taskConfig
      };

      // Create document in Firestore
      console.log('🔥 Adding task to Firestore with data:', taskData);
      const docRef = await addDoc(collection(db, 'tasks'), taskData);

      // Auto-show queue when new task is added
      setQueueVisible(true);

      console.log(`✅ Created enhanced task ${docRef.id} in Firestore:`, taskData.description);
      console.log('🔥 Task should now appear in process queue via Firestore listener');

      return docRef.id; // Return Firestore document ID
    } catch (error) {
      console.error('❌ Error creating task in Firestore:', error);
      throw new Error(`Failed to create task: ${error.message}`);
    }
  }, [userData?.uid]);

  // Enhanced updateTask - Direct Firestore updates (single source of truth)
  const updateTask = useCallback(async (taskId, updates) => {
    if (!taskId) {
      console.warn('⚠️ No task ID provided for update');
      return null;
    }

    try {
      // Check cache first for quick validation
      const cachedTask = taskCacheRef.current.get(taskId);
      if (!cachedTask) {
        console.warn(`⚠️ Task ${taskId} not found in cache, attempting direct Firestore update`);
      }

      const now = Date.now();
      const stepKey = `step_${now}`;

      // Prepare Firestore update with enhanced tracking
      const firestoreUpdates = {
        ...updates,
        lastUpdatedAt: serverTimestamp(),
      };

      // Auto-set timestamps and steps based on status changes
      if (updates.status === TASK_STATUS.PROCESSING && (!cachedTask?.startedAt)) {
        firestoreUpdates.startedAt = serverTimestamp();
        firestoreUpdates.currentStep = TASK_STEPS.API_CALL_STARTED;
      }

      if (updates.status === TASK_STATUS.WAITING_CONFIRMATION) {
        firestoreUpdates.currentStep = TASK_STEPS.WAITING_NETSUITE_CONFIRMATION;
        firestoreUpdates.confirmationTimeout = now + TASK_CONFIG.CONFIRMATION_TIMEOUT;

        // Add NetSuite sync tracking for proper confirmation
        firestoreUpdates.netsuiteSync = {
          status: 'pending',
          lastChecked: now,
          checkCount: 0,
          expectedChanges: updates.expectedChanges || [],
          originalData: updates.originalData || {},
          targetData: updates.targetData || {}
        };

        // Set up automatic timeout handling with extended timeout
        setConfirmationTimeout(taskId, TASK_CONFIG.CONFIRMATION_TIMEOUT);
      }

      if (updates.status === TASK_STATUS.COMPLETED) {
        firestoreUpdates.completedAt = serverTimestamp();
        firestoreUpdates.currentStep = TASK_STEPS.COMPLETED;
        firestoreUpdates.progress = 100;
        firestoreUpdates.netsuiteConfirmed = true;
        // Clear any pending timeout
        clearConfirmationTimeout(taskId);
      }

      if (updates.status === TASK_STATUS.FAILED || updates.status === TASK_STATUS.TIMEOUT) {
        firestoreUpdates.completedAt = serverTimestamp();
        firestoreUpdates.currentStep = TASK_STEPS.FAILED;
        if (updates.error) {
          firestoreUpdates.error = updates.error;
          firestoreUpdates.errorCategory = categorizeError(updates.error);
        }
        // Clear any pending timeout
        clearConfirmationTimeout(taskId);
      }

      if (updates.status === TASK_STATUS.RETRYING) {
        firestoreUpdates.retryCount = (cachedTask?.retryCount || 0) + 1;
        firestoreUpdates.currentStep = TASK_STEPS.RETRY_INITIATED;
        firestoreUpdates.error = null; // Clear previous error
        firestoreUpdates.startedAt = null; // Reset timing
        firestoreUpdates.completedAt = null;
      }

      // Add step tracking with enhanced metadata
      firestoreUpdates[`steps.${stepKey}`] = {
        step: firestoreUpdates.currentStep || updates.status || 'updated',
        timestamp: serverTimestamp(),
        message: updates.message || getStepMessage(firestoreUpdates.currentStep || updates.status, updates),
        progress: firestoreUpdates.progress || updates.progress || 0,
        data: updates.stepData || null,
        metadata: {
          timestamp: now,
          source: 'process_queue_update',
          updateKeys: Object.keys(updates)
        }
      };

      // Update Firestore document directly
      await updateDoc(doc(db, 'tasks', taskId), firestoreUpdates);

      console.log(`✅ Updated Firestore task ${taskId}:`, updates);

      // Update cache
      if (cachedTask) {
        const updatedTask = { ...cachedTask, ...firestoreUpdates };
        taskCacheRef.current.set(taskId, updatedTask);
      }

      return firestoreUpdates;
    } catch (error) {
      console.error(`❌ Error updating Firestore task ${taskId}:`, error);
      throw new Error(`Failed to update task: ${error.message}`);
    }
  }, []);

  // Direct Firestore update method to bypass race conditions
  const updateTaskDirect = useCallback(async (firestoreTaskId, updates) => {
    try {
      const now = Date.now();
      const stepKey = `step_${now}`;

      const firestoreUpdates = {
        ...updates,
        lastUpdatedAt: serverTimestamp(),
        [`steps.${stepKey}`]: {
          step: updates.currentStep || 'updated',
          timestamp: serverTimestamp(),
          message: updates.message || 'Task updated',
          progress: updates.progress || 0,
          data: updates.stepData || null
        }
      };

      await updateDoc(doc(db, 'tasks', firestoreTaskId), firestoreUpdates);
      console.log(`Updated Firestore task ${firestoreTaskId} directly:`, updates);
      return true;
    } catch (error) {
      console.error('Error updating Firestore task directly:', error);
      return false;
    }
  }, []);

  // Enhanced timeout management with support for NetSuite's extended processing times
  const setConfirmationTimeout = useCallback((taskId, timeoutMs = TASK_CONFIG.CONFIRMATION_TIMEOUT) => {
    // Clear any existing timeout
    const existingTimeout = timeoutRefs.current.get(taskId);
    if (existingTimeout) {
      clearTimeout(existingTimeout);
    }

    // Set new timeout with enhanced logging
    const timeoutId = setTimeout(async () => {
      console.warn(`⏰ Task ${taskId} confirmation timeout after ${timeoutMs / 1000} seconds`);

      try {
        await updateTask(taskId, {
          status: TASK_STATUS.TIMEOUT,
          error: `NetSuite confirmation timeout after ${timeoutMs / 1000} seconds`,
          errorCategory: ERROR_CATEGORIES.TIMEOUT,
          currentStep: TASK_STEPS.CONFIRMATION_TIMEOUT,
          message: `Timeout waiting for NetSuite confirmation (${timeoutMs / 1000}s)`,
          timeoutReason: 'confirmation_timeout',
          timeoutDuration: timeoutMs
        });
      } catch (error) {
        console.error(`❌ Error updating task ${taskId} on timeout:`, error);
      }

      // Remove timeout reference
      timeoutRefs.current.delete(taskId);
    }, timeoutMs);

    timeoutRefs.current.set(taskId, timeoutId);
    console.log(`⏱️ Set confirmation timeout for task ${taskId}: ${timeoutMs / 1000}s`);
  }, [updateTask]);

  // Clear confirmation timeout for a task
  const clearConfirmationTimeout = useCallback((taskId) => {
    const timeoutId = timeoutRefs.current.get(taskId);
    if (timeoutId) {
      clearTimeout(timeoutId);
      timeoutRefs.current.delete(taskId);
      console.log(`✅ Cleared confirmation timeout for task ${taskId}`);
    }
  }, []);

  // Start processing a task
  const startTask = useCallback(async (taskId) => {
    return await updateTask(taskId, { 
      status: TASK_STATUS.PROCESSING,
      startedAt: Date.now(),
      message: 'Task started processing'
    });
  }, [updateTask]);

  // Complete a task successfully
  const completeTask = useCallback(async (taskId, result = null) => {
    return await updateTask(taskId, { 
      status: TASK_STATUS.COMPLETED,
      completedAt: Date.now(),
      result,
      message: 'Task completed successfully'
    });
  }, [updateTask]);

  // Fail a task with error message
  const failTask = useCallback(async (taskId, error) => {
    const errorMessage = typeof error === 'string' ? error : error?.message || 'Unknown error';
    return await updateTask(taskId, { 
      status: TASK_STATUS.FAILED,
      completedAt: Date.now(),
      error: errorMessage,
      message: `Task failed: ${errorMessage}`
    });
  }, [updateTask]);

  // Update task progress (for tasks that support progress tracking)
  const updateProgress = useCallback(async (taskId, progress, message = null) => {
    const updates = { progress: Math.max(0, Math.min(100, progress)) };
    if (message) updates.message = message;
    return await updateTask(taskId, updates);
  }, [updateTask]);

  // Remove a task from the queue (deletes from Firestore)
  const removeTask = useCallback(async (taskId) => {
    const task = tasks.find(t => t.id === taskId || t.firestoreId === taskId);
    if (!task) return;

    // Remove from local state
    setTasks(prev => prev.filter(t => t.id !== taskId && t.firestoreId !== taskId));

    // Delete from Firestore if it exists
    if (task.firestoreId) {
      try {
        await deleteDoc(doc(db, 'tasks', task.firestoreId));
        console.log(`Deleted Firestore task ${task.firestoreId}`);
      } catch (error) {
        console.error('Error deleting Firestore task:', error);
      }
    }
  }, [tasks]);

  // Cancel a task (mark as cancelled)
  const cancelTask = useCallback(async (taskId) => {
    return await updateTask(taskId, { 
      status: TASK_STATUS.CANCELLED,
      completedAt: Date.now(),
      message: 'Task cancelled by user'
    });
  }, [updateTask]);

  // Enhanced retry functionality with exponential backoff and smart error handling
  const retryTask = useCallback(async (task) => {
    const currentRetryCount = (task.retryCount || 0) + 1;
    const maxRetries = task.maxRetries || 3;

    if (currentRetryCount > maxRetries) {
      console.warn(`Task ${task.id} has exceeded max retries (${maxRetries})`);
      return await updateTask(task.id, {
        status: TASK_STATUS.FAILED,
        error: `Maximum retry attempts (${maxRetries}) exceeded`,
        errorCategory: ERROR_CATEGORIES.SYSTEM,
        message: `Task failed after ${maxRetries} retry attempts`,
        completedAt: Date.now()
      });
    }

    // Smart retry delay based on error category
    const backoffDelay = calculateRetryDelay(task.errorCategory, currentRetryCount);

    console.log(`Retrying task ${task.id} (attempt ${currentRetryCount}/${maxRetries}) after ${backoffDelay}ms delay`);

    // Apply backoff delay
    setTimeout(async () => {
      await updateTask(task.id, {
        status: TASK_STATUS.RETRYING,
        retryCount: currentRetryCount,
        startedAt: null,
        completedAt: null,
        progress: 0,
        error: null,
        errorCategory: null,
        currentStep: TASK_STEPS.RETRY_INITIATED,
        message: `Retrying task (attempt ${currentRetryCount}/${maxRetries})`
      });

      // After a brief moment, move to queued status for processing
      setTimeout(async () => {
        await updateTask(task.id, {
          status: TASK_STATUS.QUEUED,
          message: `Task queued for retry attempt ${currentRetryCount}`
        });
      }, 100);
    }, backoffDelay);

    return task;
  }, [updateTask]);

  // Enhanced retry delay calculation with intelligent backoff strategies
  const calculateRetryDelay = useCallback((errorCategory, retryCount) => {
    const delays = TASK_CONFIG.RETRY_DELAYS[errorCategory] || TASK_CONFIG.RETRY_DELAYS[ERROR_CATEGORIES.SYSTEM];

    // Use predefined delays if available, otherwise fall back to exponential backoff
    if (retryCount <= delays.length) {
      const baseDelay = delays[retryCount - 1];
      // Add jitter to prevent thundering herd
      const jitter = Math.random() * (baseDelay * 0.1); // 10% jitter
      return baseDelay + jitter;
    }

    // Fallback to exponential backoff for excessive retries
    const maxDelay = delays[delays.length - 1];
    return maxDelay + (Math.random() * 5000); // Max delay + up to 5s jitter
  }, []);

  // Enhanced error handling with automatic retry for transient failures
  const handleTaskError = useCallback(async (taskId, error, taskData = {}) => {
    const errorMessage = typeof error === 'string' ? error : error?.message || 'Unknown error';
    const errorCategory = categorizeError(error);
    const isRetryable = ['network', 'timeout', 'netsuite_api'].includes(errorCategory);
    const currentRetryCount = taskData.retryCount || 0;
    const maxRetries = taskData.maxRetries || 3;

    console.error(`Task ${taskId} failed with ${errorCategory} error:`, errorMessage);

    if (isRetryable && currentRetryCount < maxRetries) {
      // Schedule automatic retry with exponential backoff
      const retryDelay = calculateRetryDelay(errorCategory, currentRetryCount + 1);

      console.log(`Scheduling automatic retry for task ${taskId} in ${retryDelay}ms (attempt ${currentRetryCount + 1}/${maxRetries})`);

      setTimeout(async () => {
        await updateTask(taskId, {
          status: TASK_STATUS.RETRYING,
          retryCount: currentRetryCount + 1,
          error: null,
          errorCategory: null,
          currentStep: TASK_STEPS.RETRY_INITIATED,
          message: `Automatic retry (attempt ${currentRetryCount + 1}/${maxRetries})`,
          startedAt: null,
          completedAt: null,
          progress: 0
        });

        // Move to queued after brief delay
        setTimeout(async () => {
          await updateTask(taskId, {
            status: TASK_STATUS.QUEUED,
            message: `Task queued for retry attempt ${currentRetryCount + 1}`
          });
        }, 100);
      }, retryDelay);
    } else {
      // Mark as permanently failed
      await updateTask(taskId, {
        status: TASK_STATUS.FAILED,
        error: errorMessage,
        errorCategory,
        completedAt: Date.now(),
        message: isRetryable ?
          `Task failed after ${maxRetries} retry attempts` :
          `Task failed: ${errorMessage}`
      });
    }
  }, [updateTask, calculateRetryDelay]);

  // Retry all failed tasks with intelligent batching
  const retryAllFailed = useCallback(async () => {
    const failedTasks = tasks.filter(t =>
      t.status === TASK_STATUS.FAILED ||
      t.status === TASK_STATUS.TIMEOUT
    );

    if (failedTasks.length === 0) {
      console.log('No failed tasks to retry');
      return 0;
    }

    console.log(`Retrying ${failedTasks.length} failed tasks`);

    // Group tasks by error category for intelligent retry scheduling
    const tasksByCategory = failedTasks.reduce((groups, task) => {
      const category = task.errorCategory || ERROR_CATEGORIES.SYSTEM;
      if (!groups[category]) groups[category] = [];
      groups[category].push(task);
      return groups;
    }, {});

    // Retry tasks in batches with appropriate delays
    let totalRetried = 0;
    for (const [category, categoryTasks] of Object.entries(tasksByCategory)) {
      console.log(`Retrying ${categoryTasks.length} tasks with ${category} errors`);

      // Stagger retries within each category to avoid overwhelming the system
      for (let i = 0; i < categoryTasks.length; i++) {
        const task = categoryTasks[i];
        setTimeout(() => {
          retryTask(task);
        }, i * 1000); // 1 second between retries in same category
        totalRetried++;
      }

      // Add delay between categories
      await new Promise(resolve => setTimeout(resolve, 2000));
    }

    return totalRetried;
  }, [tasks, retryTask]);

  // Get error statistics for monitoring
  const getErrorStats = useCallback(() => {
    const errorStats = {
      total: 0,
      byCategory: {},
      recentErrors: [],
      retryableCount: 0
    };

    const now = Date.now();
    const recentThreshold = now - (24 * 60 * 60 * 1000); // Last 24 hours

    tasks.forEach(task => {
      if (task.status === TASK_STATUS.FAILED || task.status === TASK_STATUS.TIMEOUT) {
        errorStats.total++;

        const category = task.errorCategory || ERROR_CATEGORIES.SYSTEM;
        errorStats.byCategory[category] = (errorStats.byCategory[category] || 0) + 1;

        // Check if task is retryable
        const hasRetriesLeft = (task.retryCount || 0) < (task.maxRetries || 3);
        const isRetryableError = [
          ERROR_CATEGORIES.NETWORK,
          ERROR_CATEGORIES.TIMEOUT,
          ERROR_CATEGORIES.NETSUITE_API
        ].includes(category);

        if (hasRetriesLeft && isRetryableError) {
          errorStats.retryableCount++;
        }

        // Add to recent errors if within threshold
        if (task.completedAt && task.completedAt > recentThreshold) {
          errorStats.recentErrors.push({
            taskId: task.id,
            description: task.description,
            error: task.error,
            category: category,
            timestamp: task.completedAt,
            retryCount: task.retryCount || 0
          });
        }
      }
    });

    // Sort recent errors by timestamp (newest first)
    errorStats.recentErrors.sort((a, b) => b.timestamp - a.timestamp);

    return errorStats;
  }, [tasks]);

  // Enhanced task cleanup with configurable retention
  const clearCompleted = useCallback(async (olderThanHours = 24) => {
    const cutoffTime = Date.now() - (olderThanHours * 60 * 60 * 1000);
    const completedTasks = tasks.filter(task =>
      (task.status === TASK_STATUS.COMPLETED ||
        task.status === TASK_STATUS.FAILED ||
        task.status === TASK_STATUS.CANCELLED) &&
      (task.completedAt || task.createdAt) < cutoffTime
    );

    if (completedTasks.length === 0) {
      console.log('No completed tasks to clear');
      return 0;
    }

    // Remove from local state
    setTasks(prev => prev.filter(task => !completedTasks.includes(task)));

    // Delete from Firestore in batches
    const batchSize = 500; // Firestore batch limit
    const deletePromises = [];

    for (let i = 0; i < completedTasks.length; i += batchSize) {
      const batchTasks = completedTasks.slice(i, i + batchSize);
      const batchPromises = batchTasks
        .filter(task => task.firestoreId)
        .map(task => deleteDoc(doc(db, 'tasks', task.firestoreId)));
      deletePromises.push(...batchPromises);
    }

    try {
      await Promise.all(deletePromises);
      console.log(`Cleared ${completedTasks.length} completed tasks older than ${olderThanHours} hours`);
      return completedTasks.length;
    } catch (error) {
      console.error('Error clearing completed tasks:', error);
      return 0;
    }
  }, [tasks]);

  // Clear only failed tasks (separate function for user control)
  const clearFailed = useCallback(async (olderThanHours = 24) => {
    const cutoffTime = Date.now() - (olderThanHours * 60 * 60 * 1000);
    const failedTasks = tasks.filter(task =>
      (task.status === TASK_STATUS.FAILED || task.status === TASK_STATUS.TIMEOUT) &&
      (task.completedAt || task.createdAt) < cutoffTime
    );

    if (failedTasks.length === 0) {
      console.log('No failed tasks to clear');
      return 0;
    }

    // Remove from local state
    setTasks(prev => prev.filter(task => !failedTasks.includes(task)));

    // Delete from Firestore
    const deletePromises = failedTasks
      .filter(task => task.firestoreId)
      .map(task => deleteDoc(doc(db, 'tasks', task.firestoreId)));

    try {
      await Promise.all(deletePromises);
      console.log(`Cleared ${failedTasks.length} failed tasks older than ${olderThanHours} hours`);
      return failedTasks.length;
    } catch (error) {
      console.error('Error clearing failed tasks:', error);
      return 0;
    }
  }, [tasks]);

  // Clear all tasks
  const clearAllTasks = useCallback(async () => {
    const allTasks = tasks.filter(task => task.firestoreId);

  // Clear local state
    setTasks([]);

    // Delete all from Firestore
    const deletePromises = allTasks.map(task =>
      deleteDoc(doc(db, 'tasks', task.firestoreId))
    );

    try {
      await Promise.all(deletePromises);
      console.log(`Cleared all ${allTasks.length} tasks`);
    } catch (error) {
      console.error('Error clearing all tasks:', error);
    }
  }, [tasks]);

  // NetSuite confirmation checker - validates actual data changes
  const checkNetSuiteConfirmation = useCallback(async (taskId, expectedChanges) => {
    try {
      const task = tasks.find(t => t.id === taskId || t.firestoreId === taskId);
      if (!task || !task.netsuiteSync) {
        console.warn(`Task ${taskId} not found or missing netsuiteSync`);
        return false;
      }

      // Check if we've exceeded maximum check attempts
      const maxChecks = 240; // 240 checks * 30 seconds = 2 hours
      const currentCheckCount = task.netsuiteSync.checkCount || 0;

      if (currentCheckCount >= maxChecks) {
        console.warn(`Task ${taskId} exceeded maximum confirmation checks (${maxChecks})`);
        await updateTask(taskId, {
          status: TASK_STATUS.TIMEOUT,
          error: 'NetSuite confirmation timeout - exceeded maximum check attempts',
          netsuiteSync: {
            ...task.netsuiteSync,
            status: 'timeout',
            timeoutAt: Date.now()
          }
        });
        return false;
      }

      // Get current data from Firestore openOrders collection with retry logic
      let currentData = [];
      let retryCount = 0;
      const maxRetries = 3;

      while (retryCount < maxRetries) {
        try {
          const currentDataQuery = query(
            collection(db, 'openOrders'),
            where('transactionid', 'in', expectedChanges.map(c => c.transactionId))
          );

          const snapshot = await getDocs(currentDataQuery);
          currentData = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
          break; // Success, exit retry loop
        } catch (queryError) {
          retryCount++;
          console.warn(`Query attempt ${retryCount} failed for task ${taskId}:`, queryError);
          if (retryCount >= maxRetries) {
            throw queryError;
          }
          // Wait before retry
          await new Promise(resolve => setTimeout(resolve, 1000 * retryCount));
        }
      }

      // Check if expected changes are reflected in current data
      let confirmedChanges = 0;
      const changeDetails = [];

      for (const expectedChange of expectedChanges) {
        const currentRow = currentData.find(row =>
          row.transactionid === expectedChange.transactionId &&
          row.lineid === expectedChange.lineId
        );

        if (!currentRow) {
          changeDetails.push({
            transactionId: expectedChange.transactionId,
            lineId: expectedChange.lineId,
            status: 'row_not_found',
            error: 'Row not found in current data'
          });
          continue;
        }

        // Check if row has error status from NetSuite operation
        if (currentRow.syncStatus === 'error') {
          changeDetails.push({
            transactionId: expectedChange.transactionId,
            lineId: expectedChange.lineId,
            status: 'netsuite_error',
            error: currentRow.errorMessage || 'NetSuite operation failed'
          });
          continue;
        }

        // Check specific field changes
        let fieldMatches = true;
        const fieldResults = {};

        for (const [field, expectedValue] of Object.entries(expectedChange.changes)) {
          const actualValue = currentRow[field];
          const matches = actualValue === expectedValue;
          fieldResults[field] = { expected: expectedValue, actual: actualValue, matches };

          if (!matches) {
            fieldMatches = false;
          }
        }

        if (fieldMatches) {
          confirmedChanges++;
          changeDetails.push({
            transactionId: expectedChange.transactionId,
            lineId: expectedChange.lineId,
            status: 'confirmed',
            fieldResults
          });
        } else {
          changeDetails.push({
            transactionId: expectedChange.transactionId,
            lineId: expectedChange.lineId,
            status: 'pending',
            fieldResults
          });
        }
      }

      const isConfirmed = confirmedChanges === expectedChanges.length;
      const hasErrors = changeDetails.some(detail => detail.status === 'netsuite_error');

      if (hasErrors) {
        // Some changes failed in NetSuite
        const errorDetails = changeDetails.filter(detail => detail.status === 'netsuite_error');
        await updateTask(taskId, {
          status: TASK_STATUS.FAILED,
          error: `NetSuite operation failed: ${errorDetails.map(d => d.error).join(', ')}`,
          netsuiteSync: {
            ...task.netsuiteSync,
            status: 'failed',
            failedAt: Date.now(),
            errorDetails: errorDetails
          }
        });

        console.log(`❌ NetSuite operation failed for task ${taskId}:`, errorDetails);
        return false;
      } else if (isConfirmed) {
        // All changes confirmed
        await updateTask(taskId, {
          status: TASK_STATUS.COMPLETED,
          progress: 100,
          message: 'NetSuite confirmation received',
          completedAt: Date.now(),
          netsuiteSync: {
            ...task.netsuiteSync,
            status: 'confirmed',
            confirmedAt: Date.now(),
            confirmedChanges: confirmedChanges,
            changeDetails: changeDetails
          }
        });

        console.log(`✅ NetSuite confirmation verified for task ${taskId}: ${confirmedChanges}/${expectedChanges.length} changes confirmed`);
        return true;
      } else {
        // Still waiting for confirmation
        await updateTask(taskId, {
          netsuiteSync: {
            ...task.netsuiteSync,
            lastChecked: Date.now(),
            checkCount: (task.netsuiteSync.checkCount || 0) + 1,
            changeDetails: changeDetails
          }
        });

        const pendingCount = expectedChanges.length - confirmedChanges;
        console.log(`⏳ NetSuite confirmation pending for task ${taskId}: ${confirmedChanges}/${expectedChanges.length} confirmed, ${pendingCount} pending`);
        return false;
      }
    } catch (error) {
      console.error(`Error checking NetSuite confirmation for task ${taskId}:`, error);
      return false;
    }
  }, [tasks, updateTask]);

  // Periodic NetSuite confirmation checker
  useEffect(() => {
    const checkInterval = setInterval(async () => {
      const waitingTasks = tasks.filter(task =>
        task.status === TASK_STATUS.WAITING_CONFIRMATION
      );

      console.log(`🔍 Periodic check: Found ${waitingTasks.length} waiting tasks`);

      for (const task of waitingTasks) {
        console.log(`📋 Task ${task.id} status:`, {
          status: task.status,
          hasNetsuiteSync: !!task.netsuiteSync,
          syncStatus: task.netsuiteSync?.status,
          hasExpectedChanges: !!task.netsuiteSync?.expectedChanges,
          expectedChangesCount: task.netsuiteSync?.expectedChanges?.length || 0
        });

        if (task.netsuiteSync?.expectedChanges?.length > 0) {
          console.log(`🔍 Checking NetSuite confirmation for task ${task.id}`);
          await checkNetSuiteConfirmation(task.id, task.netsuiteSync.expectedChanges);
        } else if (task.expectedChanges?.length > 0) {
          // Fallback: check if expectedChanges is at task level instead of netsuiteSync level
          console.log(`🔍 Checking NetSuite confirmation for task ${task.id} (fallback)`);
          await checkNetSuiteConfirmation(task.id, task.expectedChanges);
        }
      }
    }, 30000); // Check every 30 seconds

    return () => clearInterval(checkInterval);
  }, [tasks, checkNetSuiteConfirmation]);

  // Toggle queue visibility
  const toggleQueueVisibility = useCallback(() => {
    setQueueVisible(prev => !prev);
  }, []);

  // Show queue
  const showQueue = useCallback(() => {
    setQueueVisible(true);
  }, []);

  // Hide queue
  const hideQueue = useCallback(() => {
    setQueueVisible(false);
  }, []);

  // Get tasks by status
  const getTasksByStatus = useCallback((status) => {
    return tasks.filter(task => task.status === status);
  }, [tasks]);

  // Get active tasks (queued, processing, or waiting for confirmation)
  const getActiveTasks = useCallback(() => {
    return tasks.filter(task => 
      task.status === TASK_STATUS.QUEUED || 
      task.status === TASK_STATUS.PROCESSING ||
      task.status === 'waiting_confirmation'
    );
  }, [tasks]);

  // Get tasks by row key (for table integration)
  const getTasksByRowKey = useCallback((rowKey) => {
    return tasks.filter(task => task.rowKey === rowKey);
  }, [tasks]);

  // Process a task with automatic status management
  const processTask = useCallback(async (taskId, processor) => {
    try {
      await startTask(taskId);
      const result = await processor((progress, message) => {
        updateProgress(taskId, progress, message);
      });
      await completeTask(taskId, result);
      return result;
    } catch (error) {
      await failTask(taskId, error);
      throw error;
    }
  }, [startTask, updateProgress, completeTask, failTask]);

  // Get queue statistics
  const getQueueStats = useCallback(() => {
    const stats = {
      total: tasks.length,
      queued: 0,
      processing: 0,
      waiting: 0,
      completed: 0,
      failed: 0,
      cancelled: 0
    };

    tasks.forEach(task => {
      switch (task.status) {
        case TASK_STATUS.QUEUED:
          stats.queued++;
          break;
        case TASK_STATUS.PROCESSING:
          stats.processing++;
          break;
        case 'waiting_confirmation':
          stats.waiting++;
          break;
        case TASK_STATUS.COMPLETED:
          stats.completed++;
          break;
        case TASK_STATUS.FAILED:
          stats.failed++;
          break;
        case TASK_STATUS.CANCELLED:
          stats.cancelled++;
          break;
      }
    });

    return stats;
  }, [tasks]);

  // Enhanced health monitoring with comprehensive checks and NetSuite-aware timeouts
  useEffect(() => {
    if (!userData?.uid) return;

    const healthCheckInterval = setInterval(() => {
      const now = Date.now();
      const healthStats = {
        totalTasks: tasks.length,
        stuckTasks: 0,
        autoRetriedTasks: 0,
        cleanedTasks: 0,
        errors: [],
        connectionStatus: connectionStatus
      };

      // Check for stuck tasks with NetSuite-aware thresholds
      const stuckTasks = tasks.filter(task => {
        const timeSinceUpdate = now - (task.lastUpdatedAt || task.createdAt);
        const isProcessing = task.status === TASK_STATUS.PROCESSING ||
          task.status === TASK_STATUS.WAITING_CONFIRMATION ||
          task.status === TASK_STATUS.RETRYING;

        // NetSuite-aware timeout thresholds
        let timeoutThreshold = TASK_CONFIG.DEFAULT_NETSUITE_TIMEOUT; // 2 hours default

        // Adjust based on task type and current step
        if (task.currentStep === TASK_STEPS.API_CALL_STARTED) {
          timeoutThreshold = 10 * 60 * 1000; // 10 minutes for API calls
        } else if (task.currentStep === TASK_STEPS.WAITING_NETSUITE_CONFIRMATION) {
          timeoutThreshold = task.netsuiteTimeout || TASK_CONFIG.DEFAULT_NETSUITE_TIMEOUT;
        }

        const isStuck = timeSinceUpdate > timeoutThreshold;
        return isProcessing && isStuck;
      });

      healthStats.stuckTasks = stuckTasks.length;

      // Auto-recover stuck tasks with intelligent handling
      stuckTasks.forEach(async (task) => {
        console.warn(`🏥 Health Check: Detected stuck task ${task.id} (${task.type}), initiating recovery...`);
        healthStats.errors.push(`Stuck task: ${task.id} - ${task.description}`);

        try {
          await updateTask(task.id, {
            status: TASK_STATUS.TIMEOUT,
            error: 'Task stuck - automatic recovery initiated by health check',
            errorCategory: ERROR_CATEGORIES.TIMEOUT,
            message: 'Health check detected stuck task, initiating recovery',
            healthCheckTriggered: true,
            recoveryTimestamp: now
          });

          // Schedule retry with appropriate delay
          const retryDelay = calculateRetryDelay(ERROR_CATEGORIES.TIMEOUT, (task.retryCount || 0) + 1);
          setTimeout(() => {
            retryTask(task);
            healthStats.autoRetriedTasks++;
          }, retryDelay);
        } catch (error) {
          console.error(`❌ Error recovering stuck task ${task.id}:`, error);
        }
      });

      // Check for failed tasks that can be auto-retried with smart logic
      const autoRetryableTasks = tasks.filter(task => {
        const canAutoRetry = [
          ERROR_CATEGORIES.NETWORK,
          ERROR_CATEGORIES.TIMEOUT,
          ERROR_CATEGORIES.NETSUITE_API
        ].includes(task.errorCategory);

        const hasRetriesLeft = (task.retryCount || 0) < (task.maxRetries || 3);
        const timeSinceFailure = now - (task.completedAt || task.createdAt);

        // Smart retry timing based on error type
        let retryDelay = 300000; // 5 minutes default
        if (task.errorCategory === ERROR_CATEGORIES.NETWORK) {
          retryDelay = 120000; // 2 minutes for network errors
        } else if (task.errorCategory === ERROR_CATEGORIES.NETSUITE_API) {
          retryDelay = 600000; // 10 minutes for NetSuite API errors
        }

        const shouldRetry = timeSinceFailure > retryDelay;
        return task.status === TASK_STATUS.FAILED && canAutoRetry && hasRetriesLeft && shouldRetry;
      });

      // Auto-retry eligible failed tasks
      autoRetryableTasks.forEach(async (task) => {
        console.log(`Health Check: Auto-retrying failed task ${task.id} due to ${task.errorCategory} error`);
        await retryTask(task);
        healthStats.autoRetriedTasks++;
      });

      // Enhanced cleanup with configurable retention
      const oldCompletedTasks = tasks.filter(task => {
        const isCompleted = task.status === TASK_STATUS.COMPLETED;
        const taskAge = now - (task.completedAt || task.createdAt);
        const isOld = taskAge > (24 * 60 * 60 * 1000); // 24 hours
        return isCompleted && isOld;
      });

      if (oldCompletedTasks.length > 0) {
        console.log(`Health Check: Auto-cleaning ${oldCompletedTasks.length} old completed tasks`);
        clearCompleted(24); // Clear tasks older than 24 hours
        healthStats.cleanedTasks = oldCompletedTasks.length;
      }

      // Enhanced system health monitoring
      const errorRate = tasks.filter(t => t.status === TASK_STATUS.FAILED).length / Math.max(tasks.length, 1);
      const completionRate = tasks.filter(t => t.status === TASK_STATUS.COMPLETED).length / Math.max(tasks.length, 1);

      // Check for system health issues
      if (errorRate > 0.3) { // More than 30% failure rate
        console.warn(`🚨 High error rate detected: ${(errorRate * 100).toFixed(1)}% of tasks failed`);
        healthStats.errors.push(`High error rate: ${(errorRate * 100).toFixed(1)}%`);
      }

      // Check for memory leaks (too many tasks)
      if (tasks.length > 1000) {
        console.warn(`🧠 Large number of tasks detected: ${tasks.length}. Triggering cleanup.`);
        healthStats.errors.push(`Large task count: ${tasks.length}`);
        // Auto-trigger cleanup for old completed tasks
        clearCompleted(1); // Clear tasks older than 1 hour
      }

      // Log health check summary if there were any issues or periodically
      if (healthStats.stuckTasks > 0 || healthStats.autoRetriedTasks > 0 || healthStats.cleanedTasks > 0 || healthStats.errors.length > 0) {
        console.log('🏥 Health Check Summary:', {
          ...healthStats,
          errorRate: `${(errorRate * 100).toFixed(1)}%`,
          completionRate: `${(completionRate * 100).toFixed(1)}%`,
          timestamp: new Date().toISOString()
        });
      }
    }, TASK_CONFIG.HEALTH_CHECK_INTERVAL);

    healthCheckRef.current = healthCheckInterval;

    return () => {
      if (healthCheckRef.current) {
        clearInterval(healthCheckRef.current);
      }
    };
  }, [tasks, updateTask, retryTask, clearCompleted, calculateRetryDelay, connectionStatus, userData?.uid]);

  return {
    // State - Enhanced with connection monitoring
    tasks,
    queueVisible,
    isLoading,
    connectionStatus,

    // Task management - Enhanced with Firestore single source of truth
    addTask,
    updateTask,
    updateTaskDirect,
    removeTask,
    startTask,
    completeTask,
    failTask,
    cancelTask,
    retryTask,
    retryAllFailed,
    updateProgress,
    processTask,
    handleTaskError,

    // Timeout management - Enhanced for NetSuite extended processing
    setConfirmationTimeout,
    clearConfirmationTimeout,

    // Queue management - Enhanced with intelligent cleanup
    clearCompleted,
    clearFailed,
    clearAllTasks,
    toggleQueueVisibility,
    showQueue,
    hideQueue,

    // Queries and analytics - Enhanced with health monitoring
    getTasksByStatus,
    getActiveTasks,
    getTasksByRowKey,
    getQueueStats,
    getErrorStats,

    // Enhanced functionality
    calculateRetryDelay,
    checkNetSuiteConfirmation,

    // Configuration access
    config: TASK_CONFIG
  };
};

export default useProcessQueue;