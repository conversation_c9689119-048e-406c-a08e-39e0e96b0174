import React, { useState, useCallback, useMemo } from 'react';
import { Modal, Upload, Button, Select, Form, Typography, Row, Col, Tooltip, Checkbox, message } from 'antd';
import { AgGridReact } from 'ag-grid-react';
import <PERSON> from 'papaparse';
import * as XLSX from 'xlsx';
import { UploadOutlined } from '@ant-design/icons';
import { buildForecastNode } from '../../constants';

// Custom checkbox cell renderer - defined outside component to prevent re-creation
const CheckboxRenderer = (params) => {
  return (
    <Checkbox
      checked={params.value}
      onChange={(e) => {
        params.handleSelection(params.data.id, e.target.checked);
      }}
      disabled={params.data.errors.length > 0}
    />
  );
};

const UploadModal = ({ 
  open, 
  onCancel, 
  onUploadComplete, 
  rowData, 
  next12Months 
}) => {
  // Upload state variables
  const [uploadStep, setUploadStep] = useState(1);
  const [uploadedFile, setUploadedFile] = useState(null);
  const [uploadedRawData, setUploadedRawData] = useState([]);
  const [uploadedProcessedData, setUploadedProcessedData] = useState([]);
  const [selectedSheet, setSelectedSheet] = useState(null);
  const [availableSheets, setAvailableSheets] = useState([]);
  const [uploadMode, setUploadMode] = useState('replace');
  const [uploadFormat, setUploadFormat] = useState('database');
  const [uploadError, setUploadError] = useState(null);
  const [uploading, setUploading] = useState(false);

  // Reset all upload state
  const resetUploadState = () => {
    setUploadStep(1);
    setUploadedFile(null);
    setUploadedRawData([]);
    setUploadedProcessedData([]);
    setSelectedSheet(null);
    setAvailableSheets([]);
    setUploadError(null);
    setUploadFormat('database');
    setUploadMode('replace');
    setUploading(false);
  };

  // Handle checkbox selection change
  const handlePreviewRowSelection = useCallback((rowId, selected) => {
    setUploadedProcessedData(prevData => {
      const newData = [...prevData];
      const rowIndex = newData.findIndex(row => row.id === rowId);
      if (rowIndex >= 0) {
        newData[rowIndex].selected = selected;
      }
      return newData;
    });
  }, []);

  // Memoized column definitions for preview grid
  const previewColumnDefs = useMemo(() => {
    const baseColumns = [
      {
        headerName: 'Select',
        field: 'selected',
        width: 80,
        cellRenderer: 'checkboxRenderer',
        cellRendererParams: {
          handleSelection: handlePreviewRowSelection
        },
        filter: false,
        sortable: false,
        pinned: 'left'
      },
      {
        headerName: 'Status',
        field: 'statusDisplay',
        width: 100,
        valueGetter: params => {
          const errors = params.data.errors || [];
          const warnings = params.data.warnings || [];
          
          if (errors.length > 0) {
            return 'Error';
          } else if (warnings.length > 0) {
            return 'Warning';
          } else {
            return 'Valid';
          }
        },
        cellRenderer: params => {
          const errors = params.data.errors || [];
          const warnings = params.data.warnings || [];
          
          if (errors.length > 0) {
            return (
              <Tooltip title={errors.join(', ')}>
                <span style={{ color: '#ff4d4f', fontWeight: 'bold' }}>
                  ❌ Error
                </span>
              </Tooltip>
            );
          } else if (warnings.length > 0) {
            return (
              <Tooltip title={warnings.join(', ')}>
                <span style={{ color: '#faad14', fontWeight: 'bold' }}>
                  ⚠️ Warning
                </span>
              </Tooltip>
            );
          } else {
            return <span style={{ color: '#52c41a', fontWeight: 'bold' }}>✅ Valid</span>;
          }
        },
        filter: 'agSetColumnFilter',
        filterParams: {
          values: ['Valid', 'Warning', 'Error']
        },
        pinned: 'left'
      },
      {
        headerName: 'UPC',
        field: 'upc',
        width: 120,
        filter: true,
        editable: true,
        cellStyle: params => params.data.errors.includes('Missing UPC') ? { backgroundColor: '#fff2f0' } : null
      },
      {
        headerName: 'Region',
        field: 'region',
        width: 100,
        filter: true,
        editable: true
      },
      {
        headerName: 'Division',
        field: 'division',
        width: 100,
        filter: true,
        editable: true
      },
      {
        headerName: 'Class',
        field: 'class',
        width: 100,
        filter: true,
        editable: true
      },
      {
        headerName: 'Channel',
        field: 'channel',
        width: 100,
        filter: true,
        editable: true
      },
      {
        headerName: 'Customer',
        field: 'customer',
        width: 120,
        filter: true,
        editable: true
      },
      {
        headerName: 'Forecast Node',
        field: 'forecastNode',
        width: 120,
        filter: true
      }
    ];

    // Add month columns
    const monthColumns = (next12Months || []).map(month => ({
      headerName: month,
      field: month,
      width: 80,
      filter: 'agNumberColumnFilter',
      editable: true,
      valueFormatter: params => params.value || 0,
      cellEditor: 'agNumberCellEditor'
    }));

    return [...baseColumns, ...monthColumns];
  }, [next12Months, handlePreviewRowSelection]);

  // Step 1: File Upload
  const handleFileUpload = async (file) => {
    setUploading(true);
    setUploadError(null);
    
    try {
      if (file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || 
          file.name.endsWith('.xlsx') || file.name.endsWith('.xls')) {
        // Handle Excel files
        const reader = new FileReader();
        reader.onload = (e) => {
          const data = new Uint8Array(e.target.result);
          const workbook = XLSX.read(data, { type: 'array' });
          
          const sheets = workbook.SheetNames;
          setAvailableSheets(sheets);
          setSelectedSheet(sheets[0]);
          setUploadedFile(file);
          setUploadStep(2);
          setUploading(false);
        };
        reader.readAsArrayBuffer(file);
      } else if (file.name.endsWith('.csv')) {
        // Handle CSV files
        const reader = new FileReader();
        reader.onload = (e) => {
          const parsed = Papa.parse(e.target.result, { header: true, skipEmptyLines: true });
          setUploadedRawData(parsed.data);
          setUploadedFile(file);
          setUploadStep(2);
          setUploading(false);
        };
        reader.readAsText(file);
      } else {
        throw new Error('Unsupported file format. Please upload CSV or Excel files only.');
      }
    } catch (error) {
      setUploadError(error.message);
      setUploading(false);
    }
    
    return false; // Prevent default upload
  };

  // Step 2: Format Selection and Sheet Selection
  const handleFormatSelection = () => {
    if (!uploadedFile) return;
    
    setUploading(true);
    
    try {
      if (uploadedFile.name.endsWith('.csv')) {
        // CSV data is already loaded
        setUploadStep(3);
        // Add small delay to show loading state, then process data
        setTimeout(() => {
          processUploadData();
          setUploading(false);
        }, 100);
      } else {
        // Parse Excel sheet
        const reader = new FileReader();
        reader.onload = (e) => {
          const data = new Uint8Array(e.target.result);
          const workbook = XLSX.read(data, { type: 'array' });
          const worksheet = workbook.Sheets[selectedSheet];
          const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
          
          if (jsonData.length > 0) {
            const headers = jsonData[0];
            const rows = jsonData.slice(1).map(row => {
              const obj = {};
              headers.forEach((header, index) => {
                obj[header] = row[index];
              });
              return obj;
            });
            setUploadedRawData(rows);
          }
          setUploadStep(3);
          // Add delay and process data
          setTimeout(() => {
            processUploadData();
            setUploading(false);
          }, 100);
        };
        reader.readAsArrayBuffer(uploadedFile);
      }
    } catch (error) {
      setUploadError(error.message);
      setUploading(false);
    }
  };

  // Step 3: Process and validate data
  const processUploadData = () => {
    if (!uploadedRawData || uploadedRawData.length === 0) {
      setUploadError('No data to process');
      return;
    }

    console.log('Processing upload data:', {
      format: uploadFormat,
      rowCount: uploadedRawData.length,
      firstRowKeys: uploadedRawData[0] ? Object.keys(uploadedRawData[0]) : [],
      next12Months: next12Months
    });

    const processedData = uploadedRawData.map((row, index) => {
      const errors = [];
      const warnings = [];

      // Validate required fields
      if (!row.upc || row.upc.toString().trim() === '') {
        errors.push('Missing UPC');
      }

      let processedRow;

      if (uploadFormat === 'database') {
        // Check if this is long format (Month + Quantity columns) or wide format (separate month columns)
        const hasMonthColumn = row.hasOwnProperty('Month');
        const hasQuantityColumn = row.hasOwnProperty('Quantity');
        
        if (hasMonthColumn && hasQuantityColumn) {
          // Long format: Each row is one month of data for one item
          // We need to group by item and spread months across columns
          
          // For now, create the base row structure and we'll aggregate later
          processedRow = {
            id: `${row.upc || ''}_${row.forecast_node || ''}_temp_${index}`,
            selected: errors.length === 0,
            errors,
            warnings,
            upc: row.upc || '',
            region: row.region || '',
            division: row.division || '',
            class: row.class || '',
            channel: row.channel || '',
            customer: row.customer || '',
            productType: row.productType || row.producttype || '',
            color: row.color || '',
            size: row.size || '',
            forecastMethod: row.forecastMethod || row.forecast_method || 'manual',
            forecastNode: row.forecastNode || row.forecast_node || buildForecastNode({
              region: row.region,
              division: row.division,
              class: row.class,
              channel: row.channel,
              customer: row.customer
            }),
            originalRow: row,
            monthData: row.Month,
            quantityData: row.Quantity || 0
          };
          
          // Initialize all months to 0
          (next12Months || []).forEach(month => {
            processedRow[month] = 0;
          });
          
                     // Parse the month from the CSV and set the appropriate month column
           if (row.Month) {
             const monthStr = String(row.Month).trim();
             let quantity = 0;
             
             // Debug: log month values for first few rows
             if (index < 5) {
               console.log(`Row ${index}: Month="${monthStr}", Quantity="${row.Quantity}"`);
             }
             
             if (row.Quantity !== undefined && row.Quantity !== null && row.Quantity !== '') {
               quantity = row.Quantity;
               if (typeof quantity === 'string') {
                 quantity = quantity.replace(/,/g, '').replace(/\$/g, '');
                 quantity = isNaN(Number(quantity)) ? 0 : Number(quantity);
               }
               // Round to nearest integer
               quantity = Math.round(quantity);
             }
            
                         // Try to match the month string to our expected format
             const matchedMonth = (next12Months || []).find(expectedMonth => {
               const [expectedMonthName, expectedYear] = expectedMonth.split(' ');
               const expectedMonthLower = expectedMonthName.toLowerCase();
               
               // Try different month formats
               // Format: Jan-24, Jan-2024, Jan 24, Jan 2024
               const monthMatch = monthStr.match(/^([A-Za-z]{3,9})[-\s]?(\d{2,4})$/i);
               if (monthMatch) {
                 let [, monthName, year] = monthMatch;
                 if (year.length === 2) year = '20' + year;
                 
                 // Convert full month names to 3-letter abbreviations
                 const monthMap = {
                   'january': 'jan', 'february': 'feb', 'march': 'mar', 'april': 'apr',
                   'may': 'may', 'june': 'jun', 'july': 'jul', 'august': 'aug',
                   'september': 'sep', 'october': 'oct', 'november': 'nov', 'december': 'dec'
                 };
                 const shortMonth = monthMap[monthName.toLowerCase()] || monthName.toLowerCase().slice(0, 3);
                 
                 return shortMonth === expectedMonthLower && year === expectedYear;
               }
               
               // Format: 2024-01, YYYY-MM
               const yyyymmMatch = monthStr.match(/^(\d{4})-(\d{1,2})$/);
               if (yyyymmMatch) {
                 let [, year, monthNum] = yyyymmMatch;
                 const monthNames = ['jan', 'feb', 'mar', 'apr', 'may', 'jun', 'jul', 'aug', 'sep', 'oct', 'nov', 'dec'];
                 const monthName = monthNames[parseInt(monthNum) - 1];
                 return monthName === expectedMonthLower && year === expectedYear;
               }
               
               // Format: MM/DD/YYYY, M/D/YYYY, MM/D/YYYY, M/DD/YYYY
               const mmddyyyyMatch = monthStr.match(/^(\d{1,2})\/(\d{1,2})\/(\d{4})$/);
               if (mmddyyyyMatch) {
                 let [, monthNum, , year] = mmddyyyyMatch;
                 const monthNames = ['jan', 'feb', 'mar', 'apr', 'may', 'jun', 'jul', 'aug', 'sep', 'oct', 'nov', 'dec'];
                 const monthName = monthNames[parseInt(monthNum) - 1];
                 return monthName === expectedMonthLower && year === expectedYear;
               }
               
               return false;
             });
            
                         if (matchedMonth) {
               processedRow[matchedMonth] = quantity;
             }
          }
        } else {
          // Wide format: each row has separate month columns
        processedRow = {
          id: index,
          selected: errors.length === 0,
          errors,
          warnings,
          upc: row.upc || '',
          region: row.region || '',
          division: row.division || '',
          class: row.class || '',
          channel: row.channel || '',
          customer: row.customer || '',
          productType: row.productType || row.producttype || '',
          color: row.color || '',
          size: row.size || '',
          forecastMethod: row.forecastMethod || row.forecast_method || 'manual',
          forecastNode: row.forecastNode || row.forecast_node || buildForecastNode({
            region: row.region,
            division: row.division,
            class: row.class,
            channel: row.channel,
            customer: row.customer
          }),
          originalRow: row
        };
    
        // Parse month columns
          (next12Months || []).forEach(month => {
          let value = 0;
            
            // Parse the expected month format (e.g., "Jan 2024")
            const [expectedMonth, expectedYear] = month.split(' ');
            const expectedMonthLower = expectedMonth.toLowerCase();
          
          // Try to find matching column in various formats
          const monthKey = Object.keys(row).find(key => {
              const keyLower = key.trim().toLowerCase();
              
              // Exact match
              if (keyLower === month.trim().toLowerCase()) return true;
            
              // Handle various date formats
              // Format: Jan-24, Jan-2024
              const monYYMatch = key.match(/^([A-Za-z]{3})-?(\d{2,4})$/i);
            if (monYYMatch) {
              let [, mon, year] = monYYMatch;
              if (year.length === 2) year = '20' + year;
                if (mon.toLowerCase() === expectedMonthLower && year === expectedYear) return true;
              }
              
              // Format: Jan 24, Jan 2024
              const monSpaceMatch = key.match(/^([A-Za-z]{3})\s+(\d{2,4})$/i);
              if (monSpaceMatch) {
                let [, mon, year] = monSpaceMatch;
                if (year.length === 2) year = '20' + year;
                if (mon.toLowerCase() === expectedMonthLower && year === expectedYear) return true;
              }
              
              // Format: January 2024, Jan 2024
              const fullMonthMatch = key.match(/^([A-Za-z]{3,9})\s+(\d{4})$/i);
              if (fullMonthMatch) {
                let [, mon, year] = fullMonthMatch;
                // Convert full month names to 3-letter abbreviations
                const monthMap = {
                  'january': 'jan', 'february': 'feb', 'march': 'mar', 'april': 'apr',
                  'may': 'may', 'june': 'jun', 'july': 'jul', 'august': 'aug',
                  'september': 'sep', 'october': 'oct', 'november': 'nov', 'december': 'dec'
                };
                const shortMon = monthMap[mon.toLowerCase()] || mon.toLowerCase();
                if (shortMon === expectedMonthLower && year === expectedYear) return true;
              }
              
              // Format: 2024-01, 2024-1 (YYYY-MM)
              const yyyymmMatch = key.match(/^(\d{4})-(\d{1,2})$/);
              if (yyyymmMatch) {
                let [, year, monthNum] = yyyymmMatch;
                const monthNames = ['jan', 'feb', 'mar', 'apr', 'may', 'jun', 'jul', 'aug', 'sep', 'oct', 'nov', 'dec'];
                const monthName = monthNames[parseInt(monthNum) - 1];
                if (monthName === expectedMonthLower && year === expectedYear) return true;
              }
              
              // Format: MM/YYYY, M/YYYY
              const mmYYYYMatch = key.match(/^(\d{1,2})\/(\d{4})$/);
              if (mmYYYYMatch) {
                let [, monthNum, year] = mmYYYYMatch;
                const monthNames = ['jan', 'feb', 'mar', 'apr', 'may', 'jun', 'jul', 'aug', 'sep', 'oct', 'nov', 'dec'];
                const monthName = monthNames[parseInt(monthNum) - 1];
                if (monthName === expectedMonthLower && year === expectedYear) return true;
            }
            
            return false;
          });
          
            if (monthKey && row[monthKey] !== undefined && row[monthKey] !== null && row[monthKey] !== '') {
            value = row[monthKey];
            if (typeof value === 'string') {
                value = value.replace(/,/g, '').replace(/\$/g, '');
              value = isNaN(Number(value)) ? 0 : Number(value);
              } else if (typeof value === 'number') {
                value = value;
            }
          }
          processedRow[month] = value || 0;
        });
        }
      } else {
        // Pivoted format: one row per upc/node combination with all months as columns
        processedRow = {
          id: index,
          selected: errors.length === 0,
          errors,
          warnings,
          upc: row.upc || '',
          region: row.region || '',
          division: row.division || '',
          class: row.class || '',
          channel: row.channel || '',
          customer: row.customer || '',
          productType: row.productType || row.producttype || '',
          color: row.color || '',
          size: row.size || '',
          forecastMethod: row.forecastMethod || row.forecast_method || 'manual',
          forecastNode: row.forecastNode || row.forecast_node || buildForecastNode({
            region: row.region,
            division: row.division,
            class: row.class,
            channel: row.channel,
            customer: row.customer
          }),
          originalRow: row
        };

        // For pivoted format, month values are in separate columns
        (next12Months || []).forEach(month => {
          let value = 0;
          
          // Parse the expected month format (e.g., "Jan 2024")
                    const [expectedMonth, expectedYear] = month.split(' ');
          const expectedMonthLower = expectedMonth.toLowerCase();
          
          // Try to find matching column in various formats
          const monthKey = Object.keys(row).find(key => {
            const keyLower = key.trim().toLowerCase();
            
            // Exact match
            if (keyLower === month.trim().toLowerCase()) return true;
            
            // Handle various date formats
            // Format: Jan-24, Jan-2024
            const monYYMatch = key.match(/^([A-Za-z]{3})-?(\d{2,4})$/i);
            if (monYYMatch) {
              let [, mon, year] = monYYMatch;
              if (year.length === 2) year = '20' + year;
              if (mon.toLowerCase() === expectedMonthLower && year === expectedYear) return true;
            }
            
            // Format: Jan 24, Jan 2024
            const monSpaceMatch = key.match(/^([A-Za-z]{3})\s+(\d{2,4})$/i);
            if (monSpaceMatch) {
              let [, mon, year] = monSpaceMatch;
              if (year.length === 2) year = '20' + year;
              if (mon.toLowerCase() === expectedMonthLower && year === expectedYear) return true;
            }
            
            // Format: January 2024, Jan 2024
            const fullMonthMatch = key.match(/^([A-Za-z]{3,9})\s+(\d{4})$/i);
            if (fullMonthMatch) {
              let [, mon, year] = fullMonthMatch;
              // Convert full month names to 3-letter abbreviations
              const monthMap = {
                'january': 'jan', 'february': 'feb', 'march': 'mar', 'april': 'apr',
                'may': 'may', 'june': 'jun', 'july': 'jul', 'august': 'aug',
                'september': 'sep', 'october': 'oct', 'november': 'nov', 'december': 'dec'
              };
              const shortMon = monthMap[mon.toLowerCase()] || mon.toLowerCase();
              if (shortMon === expectedMonthLower && year === expectedYear) return true;
            }
            
            // Format: 2024-01, 2024-1 (YYYY-MM)
            const yyyymmMatch = key.match(/^(\d{4})-(\d{1,2})$/);
            if (yyyymmMatch) {
              let [, year, monthNum] = yyyymmMatch;
              const monthNames = ['jan', 'feb', 'mar', 'apr', 'may', 'jun', 'jul', 'aug', 'sep', 'oct', 'nov', 'dec'];
              const monthName = monthNames[parseInt(monthNum) - 1];
              if (monthName === expectedMonthLower && year === expectedYear) return true;
            }
            
            // Format: MM/YYYY, M/YYYY
            const mmYYYYMatch = key.match(/^(\d{1,2})\/(\d{4})$/);
            if (mmYYYYMatch) {
              let [, monthNum, year] = mmYYYYMatch;
              const monthNames = ['jan', 'feb', 'mar', 'apr', 'may', 'jun', 'jul', 'aug', 'sep', 'oct', 'nov', 'dec'];
              const monthName = monthNames[parseInt(monthNum) - 1];
              if (monthName === expectedMonthLower && year === expectedYear) return true;
            }
            
            return false;
          });
          
          if (monthKey && row[monthKey] !== undefined && row[monthKey] !== null && row[monthKey] !== '') {
            value = row[monthKey];
            if (typeof value === 'string') {
              value = value.replace(/,/g, '').replace(/\$/g, '');
              value = isNaN(Number(value)) ? 0 : Number(value);
            } else if (typeof value === 'number') {
              value = value;
            }
          }
          processedRow[month] = value || 0;
        });
      }

      // Additional validation
      if (!processedRow.forecastNode) {
        errors.push('Cannot determine forecast node');
      }

      // Check for duplicate in current data
      const existsInCurrent = rowData.some(existing => 
        existing.upc === processedRow.upc && 
        existing.forecastNode === processedRow.forecastNode
      );

      if (existsInCurrent && uploadMode === 'add') {
        warnings.push('Will update existing row');
      }

      processedRow.errors = errors;
      processedRow.warnings = warnings;
      processedRow.selected = errors.length === 0; // Auto-select valid rows

      return processedRow;
    });

    // If we detected long format data (Month + Quantity columns), we need to group and aggregate
    const hasLongFormat = uploadedRawData.length > 0 && 
                         uploadedRawData[0].hasOwnProperty('Month') && 
                         uploadedRawData[0].hasOwnProperty('Quantity');
    
    let finalProcessedData = processedData;
    
    if (hasLongFormat && uploadFormat === 'database') {
      console.log('Aggregating long format data...');
      
      // Group by UPC + forecast_node
      const groupedData = {};
      
      processedData.forEach(row => {
        const key = `${row.upc}_${row.forecastNode}`;
        
        if (!groupedData[key]) {
          // Initialize the group with the first row's data
          groupedData[key] = {
            ...row,
            id: key,
            // Reset all month values to 0
            ...Object.fromEntries((next12Months || []).map(month => [month, 0]))
          };
        }
        
        // Add the month data from this row to the grouped row
        if (row.monthData && row.quantityData !== undefined) {
          const monthStr = String(row.monthData).trim();
          
                     // Find which month column this data belongs to
           const matchedMonth = (next12Months || []).find(expectedMonth => {
             const [expectedMonthName, expectedYear] = expectedMonth.split(' ');
             const expectedMonthLower = expectedMonthName.toLowerCase();
             
             // Try different month formats
             const monthMatch = monthStr.match(/^([A-Za-z]{3,9})[-\s]?(\d{2,4})$/i);
             if (monthMatch) {
               let [, monthName, year] = monthMatch;
               if (year.length === 2) year = '20' + year;
               
               const monthMap = {
                 'january': 'jan', 'february': 'feb', 'march': 'mar', 'april': 'apr',
                 'may': 'may', 'june': 'jun', 'july': 'jul', 'august': 'aug',
                 'september': 'sep', 'october': 'oct', 'november': 'nov', 'december': 'dec'
               };
               const shortMonth = monthMap[monthName.toLowerCase()] || monthName.toLowerCase().slice(0, 3);
               
               return shortMonth === expectedMonthLower && year === expectedYear;
             }
             
             const yyyymmMatch = monthStr.match(/^(\d{4})-(\d{1,2})$/);
             if (yyyymmMatch) {
               let [, year, monthNum] = yyyymmMatch;
               const monthNames = ['jan', 'feb', 'mar', 'apr', 'may', 'jun', 'jul', 'aug', 'sep', 'oct', 'nov', 'dec'];
               const monthName = monthNames[parseInt(monthNum) - 1];
               return monthName === expectedMonthLower && year === expectedYear;
             }
             
             // Format: MM/DD/YYYY, M/D/YYYY, MM/D/YYYY, M/DD/YYYY
             const mmddyyyyMatch = monthStr.match(/^(\d{1,2})\/(\d{1,2})\/(\d{4})$/);
             if (mmddyyyyMatch) {
               let [, monthNum, , year] = mmddyyyyMatch;
               const monthNames = ['jan', 'feb', 'mar', 'apr', 'may', 'jun', 'jul', 'aug', 'sep', 'oct', 'nov', 'dec'];
               const monthName = monthNames[parseInt(monthNum) - 1];
               return monthName === expectedMonthLower && year === expectedYear;
             }
             
             return false;
           });
          
                     if (matchedMonth) {
             // Add the quantity to the appropriate month
             let quantity = row.quantityData;
             if (typeof quantity === 'string') {
               quantity = quantity.replace(/,/g, '').replace(/\$/g, '');
               quantity = isNaN(Number(quantity)) ? 0 : Number(quantity);
             } else if (typeof quantity === 'number') {
               quantity = quantity;
             } else {
               quantity = 0;
             }
             
             // Round to nearest integer
             quantity = Math.round(quantity);
             
             groupedData[key][matchedMonth] = (groupedData[key][matchedMonth] || 0) + quantity;
            
            console.log(`Grouped: ${key} -> ${matchedMonth} += ${quantity} (total: ${groupedData[key][matchedMonth]})`);
          }
        }
        
        // Merge any errors from individual rows
        if (row.errors && row.errors.length > 0) {
          groupedData[key].errors = [...new Set([...groupedData[key].errors, ...row.errors])];
          groupedData[key].selected = groupedData[key].errors.length === 0;
        }
      });
      
      finalProcessedData = Object.values(groupedData);
      console.log(`Aggregated ${processedData.length} rows into ${finalProcessedData.length} unique items`);
    }

    setUploadedProcessedData(finalProcessedData);
  };

  // Step 4: Apply changes
  const applyUploadChanges = () => {
    const selectedData = uploadedProcessedData.filter(row => row.selected);
    
    if (selectedData.length === 0) {
      message.error('Please select at least one row to upload');
      return;
    }

    // Process the data and call the callback
    const processedData = selectedData.map(row => {
      let finalRow = {
        ...row,
        region: { name: row.region },
        division: { name: row.division },
        class: { name: row.class },
        channel: { name: row.channel },
        customer: { name: row.customer },
        compoundKey: `${row.forecastNode}_${row.upc}`,
        _changes: { isNew: true },
        changeType: 'added'
      };

      // The parent component (DemandPlan.jsx) handles quantity addition for 'add' mode
      // We just pass the raw uploaded data and let it handle the logic

      return finalRow;
    });

    onUploadComplete(processedData, uploadMode);
    handleCancel();
  };

  // Navigation handlers
  const handleNext = () => {
    switch (uploadStep) {
      case 2:
        handleFormatSelection();
        break;
      case 3:
        applyUploadChanges();
        break;
      default:
        break;
    }
  };

  const handleBack = () => {
    if (uploadStep > 1) {
      setUploadStep(uploadStep - 1);
      setUploadError(null);
    }
  };

  const handleCancel = () => {
    onCancel();
    resetUploadState();
  };

  // Render step content
  const renderStepContent = () => {
    switch (uploadStep) {
      case 1:
        return (
          <div style={{ textAlign: 'center', padding: '40px 20px' }}>
            <Upload.Dragger
              beforeUpload={handleFileUpload}
              showUploadList={false}
              accept=".csv,.xlsx,.xls"
              disabled={uploading}
              style={{ padding: '40px' }}
            >
              <p className="ant-upload-drag-icon">
                <UploadOutlined style={{ fontSize: 48, color: '#1890ff' }} />
              </p>
              <p className="ant-upload-text" style={{ fontSize: 18, fontWeight: 500 }}>
                Click or drag file to upload
              </p>
              <p className="ant-upload-hint" style={{ fontSize: 14, color: '#666' }}>
                Support for CSV and Excel files (.csv, .xlsx, .xls)
              </p>
            </Upload.Dragger>
            {uploadError && (
              <div style={{ color: 'red', marginTop: 16, padding: 12, backgroundColor: '#fff2f0', borderRadius: 6 }}>
                <strong>Error:</strong> {uploadError}
              </div>
            )}
          </div>
        );

      case 2:
        return (
          <div style={{ padding: '20px' }}>
            <Typography.Title level={4}>Configure Upload Settings</Typography.Title>
            <div style={{ marginBottom: 24 }}>
              <strong>File:</strong> {uploadedFile?.name}
            </div>
            
            {availableSheets.length > 1 && (
              <Form.Item label="Select Sheet" style={{ marginBottom: 16 }}>
                <Select
                  style={{ width: '100%' }}
                  value={selectedSheet}
                  onChange={setSelectedSheet}
                  options={availableSheets.map(sheet => ({ label: sheet, value: sheet }))}
                />
              </Form.Item>
            )}

            <Form.Item label="Data Format" style={{ marginBottom: 16 }}>
              <Select
                style={{ width: '100%' }}
                value={uploadFormat}
                onChange={setUploadFormat}
                options={[
                  { 
                    label: 'Database Format', 
                    value: 'database',
                    title: 'Each row contains UPC, region, division, etc. with monthly values in separate columns'
                  },
                  // { 
                  //   label: 'Pivoted Format', 
                  //   value: 'pivoted',
                  //   title: 'One row per UPC/node combination with all months as columns'
                  // }
                ]}
              />
              <div style={{ fontSize: 12, color: '#666', marginTop: 4 }}>
                {uploadFormat === 'database' 
                  ? 'Each row contains UPC, region, division, etc. with monthly values in separate columns'
                  : 'One row per UPC/node combination with all months as columns'
                }
              </div>
            </Form.Item>

            <Form.Item label="Upload Mode" style={{ marginBottom: 0 }}>
              <Select
                style={{ width: '100%' }}
                value={uploadMode}
                onChange={setUploadMode}
                options={[
                  { 
                    label: 'Replace All Data', 
                    value: 'replace',
                    title: 'Remove all existing data and replace with uploaded data'
                  },
                  { 
                    label: 'Add/Update Data', 
                    value: 'add',
                    title: 'Add new rows or update existing rows, keep other data unchanged'
                  }
                ]}
              />
              <div style={{ fontSize: 12, color: '#666', marginTop: 4 }}>
                {uploadMode === 'replace' 
                  ? 'Remove all existing data and replace with uploaded data'
                  : 'Add new rows or update existing rows, keep other data unchanged'
                }
              </div>
            </Form.Item>
          </div>
        );

      case 3:
        if (uploading || uploadedProcessedData.length === 0) {
          return (
            <div style={{ textAlign: 'center', padding: '60px 20px' }}>
              <div style={{ marginBottom: 16 }}>
                <div style={{ display: 'inline-block', width: 40, height: 40, border: '4px solid #f3f3f3', borderTop: '4px solid #1890ff', borderRadius: '50%', animation: 'spin 1s linear infinite' }} />
              </div>
              <div style={{ fontSize: 16, fontWeight: 500, marginBottom: 8 }}>Processing Upload Data...</div>
              <div style={{ fontSize: 14, color: '#666' }}>
                {uploadedRawData.length > 0 && `Processing ${uploadedRawData.length.toLocaleString()} rows...`}
              </div>
              <style>{`
                @keyframes spin {
                  0% { transform: rotate(0deg); }
                  100% { transform: rotate(360deg); }
                }
              `}</style>
            </div>
          );
        }

        const validRows = uploadedProcessedData.filter(row => row.errors.length === 0).length;
        const errorRows = uploadedProcessedData.filter(row => row.errors.length > 0).length;
        const selectedRows = uploadedProcessedData.filter(row => row.selected).length;

        return (
          <div>
            <div style={{ marginBottom: 16, padding: 12, backgroundColor: '#f6f8fa', borderRadius: 6 }}>
              <Row gutter={24}>
                <Col span={6}>
                  <div style={{ textAlign: 'center' }}>
                    <div style={{ fontSize: 24, fontWeight: 'bold', color: '#52c41a' }}>{validRows}</div>
                    <div style={{ fontSize: 12, color: '#666' }}>Valid Rows</div>
                  </div>
                </Col>
                <Col span={6}>
                  <div style={{ textAlign: 'center' }}>
                    <div style={{ fontSize: 24, fontWeight: 'bold', color: '#ff4d4f' }}>{errorRows}</div>
                    <div style={{ fontSize: 12, color: '#666' }}>Error Rows</div>
                  </div>
                </Col>
                <Col span={6}>
                  <div style={{ textAlign: 'center' }}>
                    <div style={{ fontSize: 24, fontWeight: 'bold', color: '#1890ff' }}>{selectedRows}</div>
                    <div style={{ fontSize: 12, color: '#666' }}>Selected</div>
                  </div>
                </Col>
                <Col span={6}>
                  <div style={{ textAlign: 'center' }}>
                    <div style={{ fontSize: 24, fontWeight: 'bold' }}>{uploadedProcessedData.length}</div>
                    <div style={{ fontSize: 12, color: '#666' }}>Total Rows</div>
                  </div>
                </Col>
              </Row>
            </div>
            
            <div style={{ height: 500 }}>
              <AgGridReact
                columnDefs={previewColumnDefs}
                rowData={uploadedProcessedData}
                components={{
                  checkboxRenderer: CheckboxRenderer
                }}
                pagination={true}
                paginationPageSize={20}
                animateRows={true}
                autoSizeStrategy={{
                  type: 'fitCellContents',
                  defaultMinWidth: 80,
                }}
                sideBar={{
                  toolPanels: [
                    {
                      id: 'columns',
                      labelDefault: 'Columns',
                      labelKey: 'columns',
                      iconKey: 'columns',
                      toolPanel: 'agColumnsToolPanel',
                    },
                    {
                      id: 'filters',
                      labelDefault: 'Filters',
                      labelKey: 'filters',
                      iconKey: 'filter',
                      toolPanel: 'agFiltersToolPanel',
                    }
                  ],
                }}
              />
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  // Get modal properties
  const getModalTitle = () => {
    const titles = {
      1: 'Upload Demand Plan Data',
      2: 'Configure Upload Settings',
      3: 'Preview Upload Data'
    };
    return titles[uploadStep] || 'Upload';
  };

  const getModalWidth = () => {
    return uploadStep === 3 ? 1400 : 600;
  };

  const getFooterButtons = () => {
    const buttons = [];

    // Back button (not on step 1)
    if (uploadStep > 1) {
      buttons.push(
        <Button key="back" onClick={handleBack} disabled={uploading}>
          Back
        </Button>
      );
    }

    // Cancel button
    buttons.push(
      <Button key="cancel" onClick={handleCancel} disabled={uploading}>
        Cancel
      </Button>
    );

    // Next/Apply button
    if (uploadStep === 2) {
      buttons.push(
        <Button 
          key="next" 
          type="primary" 
          onClick={handleNext}
          loading={uploading}
          disabled={!uploadedFile || (availableSheets.length > 1 && !selectedSheet)}
        >
          Preview Data
        </Button>
      );
    } else if (uploadStep === 3) {
      const selectedCount = uploadedProcessedData.filter(row => row.selected).length;
      buttons.push(
        <Button 
          key="apply" 
          type="primary" 
          onClick={handleNext}
          disabled={selectedCount === 0}
        >
          Apply Changes ({selectedCount} rows)
        </Button>
      );
    }

    return buttons;
  };

  return (
    <Modal
      title={getModalTitle()}
      open={open}
      onCancel={handleCancel}
      footer={getFooterButtons()}
      width={getModalWidth()}
      style={uploadStep === 3 ? { top: 20 } : {}}
      maskClosable={false}
      keyboard={false}
    >
      {renderStepContent()}
    </Modal>
  );
};

export default UploadModal; 