import React, { createContext, useContext, useState, useEffect } from 'react';
import { useAuthState } from 'react-firebase-hooks/auth';
import { auth, db } from '../pages/firebase';
import { doc, getDoc } from 'firebase/firestore';

const UserContext = createContext();

export const useUser = () => {
  const context = useContext(UserContext);
  if (!context) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
};

export const UserProvider = ({ children }) => {
  const [user, loading] = useAuthState(auth);
  const [userData, setUserData] = useState(null);
  const [userLoading, setUserLoading] = useState(true);

  useEffect(() => {
    let timeouts = []; // Track active timeouts for cleanup
    let isMounted = true; // Track if component is still mounted
    
    const fetchUserData = async (userId, retryCount = 0) => {
      if (!userId) {
        setUserData(null);
        setUserLoading(false);
        return;
      }

      // Check if user is still authenticated before proceeding
      if (!user || user.uid !== userId) {
        console.log('User changed or logged out, canceling fetch for:', userId);
        return;
      }

      try {
        console.log('Fetching user data for ID:', userId, retryCount > 0 ? `(retry ${retryCount})` : '');
        const userRef = doc(db, 'users', userId);
        const userDoc = await getDoc(userRef);
        
        // Double-check user is still authenticated after async operation
        if (!user || user.uid !== userId) {
          console.log('User changed during fetch, ignoring result for:', userId);
          return;
        }
        
        console.log('User document exists:', userDoc.exists());
        console.log('User document data:', userDoc.data());
        
        if (userDoc.exists()) {
          if (isMounted) {
            setUserData(userDoc.data());
            setUserLoading(false);
          }
        } else {
          console.log('User document does not exist for ID:', userId);
          
          // If document doesn't exist and we haven't retried much, wait and retry
          // This handles the case where the document is still being created
          if (retryCount < 3) {
            console.log(`User document not found, retrying in ${1000 * (retryCount + 1)}ms...`);
            const timeoutId = setTimeout(() => {
              fetchUserData(userId, retryCount + 1);
            }, 1000 * (retryCount + 1)); // Progressive delay: 1s, 2s, 3s
            timeouts.push(timeoutId);
            return; // Don't set loading to false yet
          } else {
            console.error('User document does not exist after retries for ID:', userId);
            if (isMounted) {
              setUserData(null);
              setUserLoading(false);
            }
          }
        }
      } catch (error) {
        console.error('Error fetching user data:', error);
        
        // Check if user is still authenticated before retrying
        if (!user || user.uid !== userId) {
          console.log('User changed during error, not retrying for:', userId);
          return;
        }
        
        // If there's an error and we haven't retried much, try again
        if (retryCount < 2) {
          console.log(`Error fetching user data, retrying in ${2000 * (retryCount + 1)}ms...`);
          const timeoutId = setTimeout(() => {
            fetchUserData(userId, retryCount + 1);
          }, 2000 * (retryCount + 1)); // Progressive delay: 2s, 4s
          timeouts.push(timeoutId);
          return; // Don't set loading to false yet
        } else {
          if (isMounted) {
            setUserData(null);
            setUserLoading(false);
          }
        }
      }
    };

    if (!loading && user?.uid) {
      setUserLoading(true); // Ensure we show loading state
      fetchUserData(user.uid);
    } else if (!loading && !user) {
      if (isMounted) {
        setUserData(null);
        setUserLoading(false);
      }
    }

    // Cleanup function to cancel pending timeouts
    return () => {
      isMounted = false; // Mark as unmounted
      timeouts.forEach(timeoutId => {
        clearTimeout(timeoutId);
      });
      timeouts = [];
    };
  }, [user, loading]);

  const value = {
    user,
    userData,
    loading: loading || userLoading,
    isAuthenticated: !!user && !!userData,
  };

  return (
    <UserContext.Provider value={value}>
      {children}
    </UserContext.Provider>
  );
}; 