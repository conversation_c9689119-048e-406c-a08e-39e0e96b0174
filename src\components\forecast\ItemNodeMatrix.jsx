import React, { useState, useEffect, useRef, useCallback, useMemo, useImperativeHandle } from 'react';
import { Spin, Button, Select, Modal, Input, Switch, message, Tooltip, Popconfirm, Row, Col, Upload, Radio, Progress } from 'antd';
import { AgGridReact } from 'ag-grid-react';
import { themeBalham } from 'ag-grid-community';
import { api, db } from '../../pages/firebase';
import { useUser } from '../../contexts/UserContext';
import { onSnapshot, writeBatch, doc, collection, addDoc, setDoc, deleteDoc, query, where, or } from 'firebase/firestore';
import { SaveOutlined, DeleteOutlined, EyeOutlined, EyeInvisibleOutlined, PlusOutlined, MinusOutlined, EditOutlined, UndoOutlined, UploadOutlined, CloseCircleOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';


const ItemNodeMatrix = () => {
  const { userData } = useUser();
  const gridRef = useRef(null);

  const [loading, setLoading] = useState(true);
  const [items, setItems] = useState([]);
  const [forecastNodes, setForecastNodes] = useState([]);
  const [itemNodeMatrix, setItemNodeMatrix] = useState([]);
  const [data, setData] = useState([]);
  const [selectedRows, setSelectedRows] = useState([]);
  const [bulkEditModalVisible, setBulkEditModalVisible] = useState(false);
  const [bulkEditAction, setBulkEditAction] = useState('add'); // 'add' | 'remove' | 'replace'
  const [bulkEditNodes, setBulkEditNodes] = useState([]);
  const [saveViewModalVisible, setSaveViewModalVisible] = useState(false);
  const [newViewName, setNewViewName] = useState('');
  // upload modal state
  const [uploadModalVisible, setUploadModalVisible] = useState(false);
  const [uploadedData, setUploadedData] = useState({});
  const [uploadMode, setUploadMode] = useState('replace');
  const [uploadLoading, setUploadLoading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);


  const fetchItemForecastData = async () => {
    const results = await api.getItemNodeMatrixDataOnCall();
    const { items, forecastNodes } = results.data;
    setItems(items);
    setForecastNodes(forecastNodes);
    return;
    // change the data to be a list of objects with the following keys: upc, forecastNodes, ..item data
  };

  useEffect(() => {
    fetchItemForecastData();
  }, []);
  const fetchFirestoreData = async () => {
    const unsubscribe = onSnapshot(collection(db, 'itemNodeMatrix'), (snapshot) => {
      console.log('snapshot', snapshot.docs.length);
      const data = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
      setItemNodeMatrix(data);

      // Update data state immediately when Firestore changes
      const finalData = items.map(item => {
        const itemNodeMatrixData = data.find(node => node.upc === item.upc);
        return {
          id: item.upc,
          upc: item.upc,
          forecastNodes: itemNodeMatrixData?.forecastNodes || [],
          ...item
        };
      });
      setData(finalData);
      setLoading(false);
    });
    return () => unsubscribe();
  };
  useEffect(() => {
    if (items.length > 0) {
      fetchFirestoreData();
    }
  }, [items]);

  const onCellValueChanged = useCallback((params) => {
    console.log('onCellValueChanged', params);
    if (typeof params.newValue === 'string') {
      params.newValue = params.newValue.split(',').map(node => node.trim());
    }

    // Update Firestore
    setDoc(doc(db, 'itemNodeMatrix', params.data.id), {
      id: params.data.id,
      upc: params.data.upc,
      updatedAt: new Date(),
      forecastNodes: params.newValue
    }, { merge: true });

    // Update the grid data immediately for better UX
    setData(prevData => {
      const newData = [...prevData];
      const rowIndex = newData.findIndex(row => row.id === params.data.id);
      if (rowIndex !== -1) {
        newData[rowIndex] = { ...newData[rowIndex], forecastNodes: params.newValue };
      }
      return newData;
    });
  }, []); // Remove data dependency to avoid infinite loops

  // Handle bulk edit
  const handleBulkEdit = (action, nodes) => {
    const selectedRows = gridRef.current?.api?.getSelectedRows() || [];
    if (selectedRows.length === 0) {
      message.warning('Please select items first');
      return;
    }
    const tempData = [...data];
    switch (action) {
      case 'add':
        for (const row of selectedRows) {
          const upc = row.upc;
          const newNodes = [...new Set([...row.forecastNodes, ...nodes])];
          setDoc(doc(db, 'itemNodeMatrix', `${upc}`), {
            upc: upc,
            forecastNodes: newNodes,
            updatedAt: new Date()
          }, { merge: true });
          const rowIndex = tempData.findIndex(row => row.id === upc);
          if (rowIndex !== -1) {
            tempData[rowIndex].forecastNodes = newNodes;
          } else {
            // add the row to the tempData
            tempData.push({
              id: upc,
              upc: upc,
              forecastNodes: newNodes,
              updatedAt: new Date()
            });
          }
        }
        break;
      case 'remove':
        for (const row of selectedRows) {
          const upc = row.upc;
          setDoc(doc(db, 'itemNodeMatrix', `${upc}`), {
            upc: upc,
            forecastNodes: row.forecastNodes.filter(node => !nodes.includes(node)),
            updatedAt: new Date()
          }, { merge: true });
          const rowIndex = tempData.findIndex(row => row.id === upc);
          if (rowIndex !== -1) {
            tempData[rowIndex].forecastNodes = row.forecastNodes.filter(node => !nodes.includes(node));
          }
        }
        break;
      case 'replace':
        for (const row of selectedRows) {
          const upc = row.upc;
          setDoc(doc(db, 'itemNodeMatrix', `${upc}`), {
            upc: upc,
            forecastNodes: nodes,
            updatedAt: new Date()
          }, { merge: true });
          const rowIndex = tempData.findIndex(row => row.id === upc);
          if (rowIndex !== -1) {
            tempData[rowIndex].forecastNodes = nodes;
          } else {
            tempData.push({
              id: upc,
              upc: upc,
              forecastNodes: nodes,
              updatedAt: new Date()
            });
          }
        }
        break;
      case 'clear':
        for (const row of selectedRows) {
          const upc = row.upc;
          setDoc(doc(db, 'itemNodeMatrix', `${upc}`), {
            upc: upc,
            forecastNodes: [],
            updatedAt: new Date()
          }, { merge: true });
          const rowIndex = tempData.findIndex(row => row.id === upc);
          if (rowIndex !== -1) {
            tempData[rowIndex].forecastNodes = [];
          }
        }
        break;
      default:
        message.warning('Invalid action');
        break;
    }
    setData(tempData);
    setBulkEditModalVisible(false);
  };

  // Column definitions
  const columnDefs = useMemo(() => [
    {
      headerName: 'Forecast Nodes',
      field: 'forecastNodes',
      width: 300,
      editable: true,
      cellEditor: 'agRichSelectCellEditor',
      cellEditorParams: {
        values: forecastNodes.map(node => node.value),
        multiSelect: true,
      },
      sortable: false,
      filter: 'agSetColumnFilter',
      filterParams: {
        values: forecastNodes.map(node => node.value),
        suppressMiniFilter: true
      },
      valueGetter: (params) => {
        return params.data.forecastNodes || [];
      },
      cellRenderer: (params) => {
        // Ensure forecastNodes is always an array and handle all edge cases
        let forecastNodesArray = [];

        if (params?.data?.forecastNodes) {
          if (Array.isArray(params.data.forecastNodes)) {
            forecastNodesArray = params.data.forecastNodes;
          } else if (typeof params.data.forecastNodes === 'string') {
            // Convert string to array if it's a string
            forecastNodesArray = params.data.forecastNodes
              .split(/[,;\n]/)
              .map(node => node.trim())
              .filter(node => node.length > 0);
          }
        }

        return (
          <div style={{ display: 'flex', gap: 6, flexWrap: 'wrap' }}>
            {forecastNodesArray.length === 0 && <span style={{ color: '#d9d9d9' }}>No nodes</span>}
            {forecastNodesArray.map((node, idx) => {
              // node may be a value or an object; find the node object in forecastNodes
              const nodeObj = forecastNodes.find(n => n.value === (node.value || node)) || {};
              const color = nodeObj.color || '#d9d9d9';
              const label = nodeObj.label || nodeObj.value || node.value || node;
              return (
                <span
                  key={label + idx}
                  title={label}
                  style={{
                    display: 'inline-block',
                    width: 16,
                    height: 16,
                    borderRadius: '50%',
                    background: color,
                    border: '1px solid #d9d9d9',
                    cursor: 'pointer',
                  }}
                />
              );
            })}
          </div>
        );
      }
    },
    {
      headerName: 'UPC',
      field: 'upc',
      width: 150,
      filter: 'agSetColumnFilter',
      sortable: true
    },
    {
      headerName: 'Division',
      field: 'productdivision',
      width: 120,
      filter: 'agSetColumnFilter',
      sortable: true
    },
    {
      headerName: 'Spec',
      field: 'productspecification',
      width: 120,
      filter: 'agSetColumnFilter',
      sortable: true
    },
    {
      headerName: 'Form',
      field: 'productform',
      width: 120,
      filter: 'agSetColumnFilter',
      sortable: true
    },
    {
      headerName: 'Category',
      field: 'productcategory',
      width: 120,
      filter: 'agSetColumnFilter',
      sortable: true
    },
    {
      headerName: 'Product',
      field: 'producttype',
      width: 250,
      filter: 'agSetColumnFilter',
      sortable: true
    },
    {
      headerName: 'Color',
      field: 'color',
      width: 100,
      filter: 'agSetColumnFilter',
      sortable: true
    },
    {
      headerName: 'Size',
      field: 'size',
      width: 80,
      filter: 'agSetColumnFilter',
      sortable: true
    },
    {
      headerName: 'Life Status',
      field: 'lifestatus',
      width: 120,
      filter: 'agSetColumnFilter',
      sortable: true
    },
    {
      headerName: 'On Hand',
      field: 'on_hand',
      width: 100,
      type: 'numericColumn',
      filter: 'agNumberColumnFilter',
      valueFormatter: params => params.value?.toLocaleString() || '0'
    },
    {
      headerName: 'Available',
      field: 'available',
      width: 100,
      type: 'numericColumn',
      filter: 'agNumberColumnFilter',
      valueFormatter: params => params.value?.toLocaleString() || '0'
    },
    {
      headerName: 'On Order',
      field: 'on_order',
      width: 100,
      type: 'numericColumn',
      filter: 'agNumberColumnFilter',
      valueFormatter: params => params.value?.toLocaleString() || '0'
    }
  ], [forecastNodes]);

  // Proper CSV parsing function that handles quoted values
  const parseCSVLine = useCallback((line) => {
    const result = [];
    let current = '';
    let inQuotes = false;

    for (let i = 0; i < line.length; i++) {
      const char = line[i];

      if (char === '"') {
        inQuotes = !inQuotes;
      } else if (char === ',' && !inQuotes) {
        result.push(current.trim());
        current = '';
      } else {
        current += char;
      }
    }

    // Add the last field
    result.push(current.trim());
    return result;
  }, []);

  // Handle file upload - simplified version
  const handleFileUpload = useCallback((file) => {
    setUploadLoading(true);
    setUploadedData({});

    const processData = (rawData) => {
      try {
        const lines = rawData.split('\n').filter(line => line.trim());
        if (lines.length === 0) {
          message.error('File is empty');
          setUploadLoading(false);
          return;
        }

        // Parse header row
        const headerValues = parseCSVLine(lines[0]);
        const headers = headerValues.map(h => h.toLowerCase().replace(/"/g, ''));
        const upcIndex = headers.findIndex(h => h.includes('upc'));
        const forecastNodeIndex = headers.findIndex(h => h.includes('forecast nodes') || h.includes('forecast_node'));

        if (upcIndex === -1) {
          message.error('File must contain a UPC column');
          setUploadLoading(false);
          return;
        }

        // Parse data and build UPC to nodes mapping
        const upcToNodes = {};
        for (let i = 1; i < lines.length; i++) {
          if (!lines[i].trim()) continue;
          
          const values = parseCSVLine(lines[i]);
          const upc = values[upcIndex]?.trim();
          if (!upc) continue;

          let forecastNodesStr = '';
          if (forecastNodeIndex !== -1 && values[forecastNodeIndex]) {
            forecastNodesStr = values[forecastNodeIndex].trim().replace(/"/g, '');
          }

          const nodes = forecastNodesStr
            ? forecastNodesStr.split(',').map(node => node.trim()).filter(node => node)
            : [];

          upcToNodes[upc] = nodes;
        }

        if (Object.keys(upcToNodes).length === 0) {
          message.warning('No valid UPCs found in the file');
          setUploadLoading(false);
          return;
        }

        setUploadedData(upcToNodes);
        message.success(`Successfully parsed ${Object.keys(upcToNodes).length} UPCs`);
      } catch (error) {
        console.error('Error parsing file:', error);
        message.error('Failed to parse file: ' + error.message);
      } finally {
        setUploadLoading(false);
      }
    };

    const readExcelFile = (file) => {
      import('xlsx').then(XLSX => {
        const reader = new FileReader();
        reader.onload = (e) => {
          try {
            const data = new Uint8Array(e.target.result);
            const workbook = XLSX.read(data, { type: 'array' });
            const firstSheet = workbook.Sheets[workbook.SheetNames[0]];
            const csv = XLSX.utils.sheet_to_csv(firstSheet);
            processData(csv);
          } catch (err) {
            message.error('Failed to parse Excel file');
            setUploadLoading(false);
          }
        };
        reader.onerror = () => {
          message.error('Failed to read file');
          setUploadLoading(false);
        };
        reader.readAsArrayBuffer(file);
      }).catch(() => {
        message.error('Failed to load Excel parser');
        setUploadLoading(false);
      });
    };

    const fileName = file.name || '';
    const ext = fileName.split('.').pop().toLowerCase();
    if (ext === 'csv') {
      const reader = new FileReader();
      reader.onload = (e) => processData(e.target.result);
      reader.onerror = () => {
        message.error('Failed to read file');
        setUploadLoading(false);
      };
      reader.readAsText(file);
    } else if (ext === 'xlsx' || ext === 'xls') {
      readExcelFile(file);
    } else {
      message.error('Unsupported file type. Please upload a .csv, .xlsx, or .xls file.');
      setUploadLoading(false);
    }
    return false;
  }, [parseCSVLine]);


  const onClearFilters = () => {
    if (gridRef.current && gridRef.current.api) {
      gridRef.current.api.setFilterModel(null);
    }
  };

  // Handle upload confirmation - simplified to mirror bulk edit
  // Use Firestore batch writes, processing 500 at a time for efficiency and atomicity
  const handleUploadConfirm = useCallback(async () => {
    if (!uploadedData || Object.keys(uploadedData).length === 0) {
      message.warning('No data to apply');
      return;
    }

    setUploadLoading(true);

    try {
      const tempData = [...data];
      let processedCount = 0;
      const updates = [];

      // Prepare updates array for batching
      Object.entries(uploadedData).forEach(([upc, nodes]) => {
        // Validate UPC exists in our data
        const itemExists = items.some(item => item.upc === upc);
        if (!itemExists) {
          console.warn(`UPC ${upc} not found in item data`);
          return;
        }

        // Validate nodes exist in forecast nodes
        const validNodes = nodes.filter(nodeCode => {
          const exists = forecastNodes.some(node => node.value === nodeCode);
          if (!exists && nodeCode) {
            console.warn(`Forecast node ${nodeCode} not found in system`);
          }
          return exists;
        });

        // Find or create the row in our data
        let rowIndex = tempData.findIndex(row => row.upc === upc);
        let currentNodes = [];
        if (rowIndex !== -1) {
          currentNodes = tempData[rowIndex].forecastNodes || [];
        }

        let newNodes = [];
        switch (uploadMode) {
          case 'add':
            newNodes = [...new Set([...currentNodes, ...validNodes])];
            break;
          case 'remove':
            newNodes = currentNodes.filter(node => !validNodes.includes(node));
            break;
          case 'replace':
            newNodes = validNodes;
            break;
          case 'clear':
            newNodes = [];
            break;
          default:
            return;
        }

        // Prepare update for batch
        updates.push({
          upc,
          newNodes,
        });

        // Update local data
        if (rowIndex !== -1) {
          tempData[rowIndex].forecastNodes = newNodes;
        } else {
          // Add new row if it doesn't exist
          const itemData = items.find(item => item.upc === upc);
          if (itemData) {
            tempData.push({
              id: upc,
              upc: upc,
              forecastNodes: newNodes,
              ...itemData
            });
          }
        }

        processedCount++;
      });

      // Batch process updates, 500 at a time
      for (let i = 0; i < updates.length; i += 500) {
        setUploadProgress((i + 500) / updates.length);
        const batch = writeBatch(db);
        updates.slice(i, i + 500).forEach(({ upc, newNodes }) => {
          batch.set(
            doc(db, 'itemNodeMatrix', upc),
            {
              upc,
              forecastNodes: newNodes,
              updatedAt: new Date()
            },
            { merge: true }
          );
        });
        await batch.commit();
      }

      setData(tempData);
      setUploadModalVisible(false);
      setUploadedData({});
      setUploadProgress(0);
      setUploadLoading(false);
      message.success(`Successfully updated ${processedCount} items`);
    } catch (error) {
      console.error('Error applying uploaded data:', error);
      message.error('Failed to apply changes: ' + error.message);
    } finally {
      setUploadLoading(false);
    }
  }, [uploadedData, items, forecastNodes, uploadMode, data]);

  return (
    <div style={{ padding: '20px' }}>
      <div style={{ marginBottom: '24px' }}>
        <Row gutter={[8, 16]} align="middle">
          <Col>
            <h1 style={{ margin: 0, fontSize: '24px', fontWeight: '600' }}>
              Item Node Matrix
            </h1>
            <p style={{ margin: '8px 0 0 0', color: '#666' }}>
              Manage forecast node assignments for demand planning
            </p>
          </Col>
        </Row>
      </div>

      {/* Toolbar */}
      <Row gutter={[8, 8]} style={{ marginBottom: 16 }} align="middle">
        <Col>
          <span style={{ fontWeight: 500 }}>Selected: {selectedRows.length} items</span>
        </Col>
        <Col>
          <Button
            icon={<EditOutlined />}
            onClick={() => setBulkEditModalVisible(true)}
          // disabled={gridRef.current?.api?.getSelectedRows().length === 0}
          >
            Bulk
          </Button>
        </Col>
        <Col>
          <Button
            icon={<UploadOutlined />}
            onClick={() => setUploadModalVisible(true)}
          />
        </Col>
        <Col>
          <Tooltip title="Clear all filters">
            <Button onClick={onClearFilters} icon={<CloseCircleOutlined />} />
          </Tooltip>
        </Col>
      </Row>

      {/* Grid */}
      <div style={{ height: 'calc(100vh - 280px)', width: '100%' }}>
        {loading ? (
          <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
            <Spin size="large" />
          </div>
        ) : (
          <AgGridReact
            ref={gridRef}
            columnDefs={columnDefs}
            rowData={data}
            theme={themeBalham}
            pagination={false}
            onCellValueChanged={onCellValueChanged}
            cellSelection={true}
            rowSelection={{
              mode: 'multiRow',
              selectAll: 'filtered',
              checkboxes: true,
              headerCheckboxSelection: true
            }}
            defaultColDef={{
              resizable: true,
              sortable: true,
              filter: true
            }}
            sideBar={{
              toolPanels: [
                {
                  id: 'columns',
                  labelDefault: 'Columns',
                  labelKey: 'columns',
                  iconKey: 'columns',
                  toolPanel: 'agColumnsToolPanel'
                },
                {
                  id: 'filters',
                  labelDefault: 'Filters',
                  labelKey: 'filters',
                  iconKey: 'filter',
                  toolPanel: 'agFiltersToolPanel'
                }
              ]
            }}
          />
        )}
      </div>

      {/* Bulk Edit Modal */}
      <Modal
        title={`Bulk ${bulkEditAction.charAt(0).toUpperCase() + bulkEditAction.slice(1)}`}
        open={bulkEditModalVisible}
        onCancel={() => setBulkEditModalVisible(false)}
        onOk={() => {
          handleBulkEdit(bulkEditAction, bulkEditNodes);
          setBulkEditModalVisible(false);
        }}
      >
        <div>
          <Radio.Group onChange={(e) => setBulkEditAction(e.target.value)} value={bulkEditAction}
            options={[
              { label: 'Add', value: 'add' },
              { label: 'Remove', value: 'remove' },
              { label: 'Replace', value: 'replace' },
              { label: 'Clear', value: 'clear' }
            ]}
          />
          <div>
            <Select
              options={forecastNodes.map(node => ({ value: node.value, label: node.label }))}
              onChange={(value) => setBulkEditNodes(value)}
              mode="multiple"
              placeholder="Select forecast nodes"
              style={{ width: '100%' }}
              disabled={bulkEditAction === 'clear'}
            />
          </div>
        </div>
      </Modal>

      {/* Upload Modal */}
      <Modal
        title="Upload File"
        open={uploadModalVisible}
        onCancel={() => {
          setUploadModalVisible(false);
          setUploadedData({});
        }}
        onOk={handleUploadConfirm}
        okButtonProps={{
          disabled: !uploadedData || Object.keys(uploadedData).length === 0,
          loading: uploadLoading
        }}
      >
        <div>
          <Radio.Group onChange={(e) => setUploadMode(e.target.value)} value={uploadMode}
            options={[
              { label: 'Add', value: 'add' },
              { label: 'Remove', value: 'remove' },
              { label: 'Replace', value: 'replace' },
              { label: 'Clear', value: 'clear' },
            ]}
          />
          <div>
            <Upload
              accept=".xlsx,.csv,.xls"
              showUploadList={false}
              beforeUpload={handleFileUpload}
            >
              <Button icon={<UploadOutlined />} loading={uploadLoading}>
                Click to Upload
              </Button>
            </Upload>
            {uploadedData && Object.keys(uploadedData).length > 0 && (
              <div style={{ marginTop: 16, padding: 12, background: '#f5f5f5', borderRadius: 4 }}>
                <p>Parsed {Object.keys(uploadedData).length} UPCs:</p>
                <div style={{ maxHeight: 200, overflowY: 'auto' }}>
                  {Object.entries(uploadedData).slice(0, 10).map(([upc, nodes]) => (
                    <div key={upc} style={{ fontSize: 12, marginBottom: 4 }}>
                      <strong>{upc}:</strong> {nodes.length > 0 ? nodes.join(', ') : 'No nodes'}
                    </div>
                  ))}
                  {Object.keys(uploadedData).length > 10 && (
                    <div style={{ fontSize: 12, fontStyle: 'italic' }}>
                      ... and {Object.keys(uploadedData).length - 10} more
                    </div>
                  )}
                </div>
              </div>
            )}
            {uploadLoading && (
              <Progress percent={uploadProgress} />
            )}
          </div>
        </div>
      </Modal>

      {/* Save View Modal */}
    </div>
  );
};

export default ItemNodeMatrix;